<Window x:Class="Metabolomics.MsLima.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
        xmlns:local="clr-namespace:Metabolomics.MsLima"
        xmlns:model="clr-namespace:Metabolomics.MsLima.Model"
        xmlns:view="clr-namespace:Metabolomics.Core;assembly=MS-LIMA-CommonView"
        xmlns:controls="clr-namespace:Metabolomics.MsLima.Controls"
        xmlns:services="clr-namespace:Metabolomics.MsLima.Services"
        mc:Ignorable="d"
        Title="{Binding MainWindowTitle}"
        Height="780"
        Width="1200"
        Background="{DynamicResource WindowBackgroundBrush}"
        FontFamily="{DynamicResource PrimaryFontFamily}"
        FontSize="{DynamicResource BodyFontSize}"
        WindowStartupLocation="CenterScreen">
    <i:Interaction.Triggers>
        <i:EventTrigger EventName="Loaded">
            <i:InvokeCommandAction Command="{Binding WindowLoaded}" />
        </i:EventTrigger>
    </i:Interaction.Triggers>
    <Window.Resources>
        <model:TabMassSpectraViewIntConverter x:Key="TabMassSpectraViewIntConverter" />
        <model:TabMassSpectraTableIntConverter x:Key="TabMassSpectrumTableIntConverter" />
        <model:NullToSlashConverter x:Key="NullToSlashConverter" />
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

        <!-- Apply Modern Styles to Controls -->
        <Style TargetType="DataGrid" BasedOn="{StaticResource ModernDataGrid}"/>
        <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource ModernDataGridColumnHeader}"/>
        <Style TargetType="DataGridRow" BasedOn="{StaticResource ModernDataGridRow}"/>
        <Style TargetType="ScrollBar" BasedOn="{StaticResource ModernScrollBar}"/>
        <Style TargetType="Button" BasedOn="{StaticResource ModernButton}"/>
        <Style TargetType="TextBox" BasedOn="{StaticResource ModernTextBox}"/>
        <Style TargetType="Menu" BasedOn="{StaticResource ModernMenu}"/>
        <Style TargetType="MenuItem" BasedOn="{StaticResource ModernMenuItem}"/>

        <!-- Modern GridSplitter Style -->
        <Style TargetType="GridSplitter">
            <Setter Property="Background" Value="{DynamicResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <!-- Modern TabControl Style -->
        <Style TargetType="TabControl">
            <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="0"/>
        </Style>

        <!-- Modern TabItem Style -->
        <Style TargetType="TabItem">
            <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1,1,1,0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
            <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4,4,0,0"
                                Margin="0,0,2,0">
                            <ContentPresenter x:Name="ContentSite"
                                            VerticalAlignment="Center"
                                            HorizontalAlignment="Center"
                                            ContentSource="Header"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource SelectedBrush}"/>
                                <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource HoverBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Label Style -->
        <Style TargetType="Label">
            <Setter Property="Background" Value="{DynamicResource MenuBackgroundBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
            <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
            <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="BorderBrush" Value="{DynamicResource DividerBrush}"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="MinHeight" Value="35"/>
            <Setter Property="Padding" Value="8,8"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- Modern PlaceholderTextBox Style -->
        <Style x:Key="PlaceholderTextBox" TargetType="{x:Type TextBox}" BasedOn="{StaticResource ModernTextBox}">
            <Setter Property="TextAlignment" Value="Left"/>
            <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
            <Setter Property="MinHeight" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Padding" Value="6,2"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TextBox}">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="0"
                                SnapsToDevicePixels="True">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost"
                                            Focusable="False"
                                            HorizontalScrollBarVisibility="Hidden"
                                            VerticalScrollBarVisibility="Hidden"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                                <TextBlock x:Name="PlaceholderText"
                                           Text="{TemplateBinding Tag}"
                                           Foreground="{DynamicResource HintTextBrush}"
                                           Margin="{TemplateBinding Padding}"
                                           Visibility="Collapsed"
                                           IsHitTestVisible="False"
                                           TextAlignment="Left"
                                           VerticalAlignment="Center"
                                           FontFamily="{DynamicResource PrimaryFontFamily}"
                                           FontSize="{DynamicResource CaptionFontSize}"/>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Visible"/>
                            </Trigger>
                            <Trigger Property="IsKeyboardFocused" Value="True">
                                <Setter TargetName="PlaceholderText" Property="Visibility" Value="Collapsed"/>
                                <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource FocusBorderBrush}"/>
                                <Setter Property="BorderThickness" TargetName="border" Value="2"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource FocusBorderBrush}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Opacity" TargetName="border" Value="0.56"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <DockPanel Name="DockPanel_MainWindow">
        <!-- Modern Menu Bar with Theme Toggle -->
        <Grid DockPanel.Dock="Top" Height="50" Background="{DynamicResource MenuBackgroundBrush}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Main Menu -->
            <Menu Name="Menu_MainMenueBar" Grid.Column="0" Height="50" VerticalAlignment="Stretch" Background="Transparent" Padding="8,0" Style="{StaticResource ModernMenu}">
            <MenuItem Header="_File" Style="{StaticResource ModernMenuItem}">
                <MenuItem Name="Import" Header="_Import" Style="{StaticResource ModernMenuItem}">
                    <MenuItem Name="MenuItem_ImportMsp" Header="Import" Command="{Binding ImportFileCommand}" Style="{StaticResource ModernMenuItem}">
                        <ToolTipService.ToolTip>
                            <ToolTip Content="Import MSP/MGF file"/>
                        </ToolTipService.ToolTip>
                    </MenuItem>
                    <MenuItem Name="MenuItem_Import" Header="Import MassBank" Command="{Binding ImportMassBankFileCommand}" Style="{StaticResource ModernMenuItem}">
                        <ToolTipService.ToolTip>
                            <ToolTip Content="Import MassBank file"/>
                        </ToolTipService.ToolTip>
                    </MenuItem>
                </MenuItem>

                <MenuItem Name="Export" Header="_Export" Style="{StaticResource ModernMenuItem}">
                    <MenuItem Name="MenuItem_ExportAsMsp" Header="Export as MSP" Command="{Binding SaveAsMspCommand}" Style="{StaticResource ModernMenuItem}">
                        <ToolTipService.ToolTip>
                            <ToolTip Content="Export as MSP format"/>
                        </ToolTipService.ToolTip>
                    </MenuItem>
                    <MenuItem Name="MenuItem_ExportAsMspDropRt" Header="Export as MSP without retention time" Command="{Binding SaveAsMspWithoutRTCommand}" Style="{StaticResource ModernMenuItem}">
                        <ToolTipService.ToolTip>
                            <ToolTip Content="Export as MSP format without retention time"/>
                        </ToolTipService.ToolTip>
                    </MenuItem>
                    <MenuItem Name="MenuItem_ExportAsMgf" Header="Export as MGF" Command="{Binding SaveAsMgfCommand}" Style="{StaticResource ModernMenuItem}">
                        <ToolTipService.ToolTip>
                            <ToolTip Content="Export as MGF format including RTINSECONDS"/>
                        </ToolTipService.ToolTip>
                    </MenuItem>
                    <MenuItem Name="MenuItem_ExportAsTable" Header="Export as table for MZmine library" Command="{Binding SaveAsMzMineCommand}" Style="{StaticResource ModernMenuItem}">
                        <ToolTipService.ToolTip>
                            <ToolTip Content="Export as tab-separated format"/>
                        </ToolTipService.ToolTip>
                    </MenuItem>
                </MenuItem>
            </MenuItem>

            <MenuItem Name="Setting" Header="_Setting" Style="{StaticResource ModernMenuItem}">
                <MenuItem Name="MenuItem_Setting" Header="Setting Window" Command="{Binding StartUpSettingWindow}" Style="{StaticResource ModernMenuItem}">
                </MenuItem>
            </MenuItem>

<MenuItem Name="Utility" Header="_Utility" Style="{StaticResource ModernMenuItem}">
    <MenuItem Name="MenuItem_Utility_RemoveUnannotated" Header="Remove unannotated peaks" Command="{Binding RemoveUnannotatedCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Unannotated peaks (comment filed is empty) will be removed"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_Convert2TheoreticalMass" Header="Convert precursor m/z as theoretical m/z" Command="{Binding ConvertAccurateMassToTheoreticalMass}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Accurate m/z of precursorMz will be converted to theoretical m/z"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_DropRt" Header="Remove all retention time" Command="{Binding DropRetentionTime}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Remove retention time in all records"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_SaveCommonProductIons" Header="Calc and save common product ions" Command="{Binding SaveCommonProductIonCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Calculate the frequency of product ions in the library"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_UpdateSmilesAndInchi" Header="Update SMILES and InChI based on InChIKey" Command="{Binding UpdateSmilesAndInChiBasedOnInChIKeyCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Using additional files (InChIKey InChI SMILES)"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_CommonMetaData" Header="Update common meta information" Command="{Binding UpdateCommonMetaDataCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Using additional files (InChIKey InChI SMILES)"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <Separator/>
    <MenuItem Name="MenuItem_Utility_CheckRtDifferences" Header="Check RT differences" Command="{Binding CheckRtDifferencesCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Check for retention time differences within compound groups"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_CheckInChIKeyDifferences" Header="Check InChIKey differences" Command="{Binding CheckInChIKeyDifferencesCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Check for InChIKey differences within compound groups"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_CheckFormulaDifferences" Header="Check formula differences" Command="{Binding CheckFormulaDifferencesCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Check for formula differences within compound groups"/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_Utility_CheckAllValidation" Header="Check all validation (RT, InChIKey, Formula)" Command="{Binding CheckAllValidationCommand}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Run all validation checks: RT differences, InChIKey differences, and formula differences"/>
        </ToolTipService.ToolTip>
    </MenuItem>
</MenuItem>

<MenuItem Name="Viewer" Header="_Viewer" Style="{StaticResource ModernMenuItem}">
    <MenuItem Name="MenuItem_AllSpectra" Header="Show meta deta of all spectra" Command="{Binding StartUpWindowAllSpectra}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="Meta data of all spectra will be shown. User can change them. "/>
        </ToolTipService.ToolTip>
    </MenuItem>
    <MenuItem Name="MenuItem_ComparativeViewer" Header="Show comparative spectra viewer" Command="{Binding StartUpWindowComparativeViewer}" Style="{StaticResource ModernMenuItem}">
        <ToolTipService.ToolTip>
            <ToolTip Content="For comparing two MS2 spectra. User can also import different libraries."/>
        </ToolTipService.ToolTip>
    </MenuItem>
</MenuItem>


        </Menu>

        <!-- Dual Theme Controls: Light/Dark Toggle + Color Scheme Selector -->
        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center"
                    Margin="8,0,12,8" Panel.ZIndex="10">
            <!-- Light/Dark Theme Toggle -->
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,12,0">
                <TextBlock Text="Theme:"
                           VerticalAlignment="Center"
                           Margin="0,0,6,0"
                           Foreground="{DynamicResource SecondaryTextBrush}"
                           FontSize="{DynamicResource BodyFontSize}"/>
                <controls:ThemeToggle x:Name="ThemeToggleControl"
                                      IsDarkTheme="{Binding IsDarkTheme, RelativeSource={RelativeSource AncestorType=Window}}"
                                      ThemeChanged="ThemeToggleControl_ThemeChanged"/>
            </StackPanel>

            <!-- Color Scheme Selector -->
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="Colors:"
                           VerticalAlignment="Center"
                           Margin="0,0,6,0"
                           Foreground="{DynamicResource SecondaryTextBrush}"
                           FontSize="{DynamicResource BodyFontSize}"/>
                <controls:ColorSchemeSelector x:Name="ColorSchemeSelectorControl"
                                              CurrentColorScheme="{Binding CurrentColorScheme, RelativeSource={RelativeSource AncestorType=Window}}"
                                              ColorSchemeChanged="ColorSchemeSelectorControl_ColorSchemeChanged"/>
            </StackPanel>
        </StackPanel>
    </Grid>

    <!-- Progress Bar Overlay for Import Operations -->
    <Grid DockPanel.Dock="Top" Height="4" Background="Transparent"
          Visibility="{Binding IsImportInProgress, Converter={StaticResource BooleanToVisibilityConverter}}"
          Panel.ZIndex="100">
        <ProgressBar Name="ImportProgressBar"
                     Value="{Binding ImportProgress}"
                     Minimum="0"
                     Maximum="100"
                     Height="4"
                     Background="{DynamicResource SurfaceBrush}"
                     Foreground="{DynamicResource AccentBrush}"
                     BorderThickness="0"
                     IsIndeterminate="{Binding IsImportIndeterminate}"/>
    </Grid>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="0.7*" MinHeight="400"/>
                <RowDefinition Height="5"/> <!-- For row splitter -->
                <RowDefinition Height="1*" MinHeight="200"/>
            </Grid.RowDefinitions>

            <Grid Name="Grid_Top" Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" MinWidth="250" />
                    <ColumnDefinition Width="2" />
                    <ColumnDefinition Width="1.5*" MinWidth="250" />
                    <ColumnDefinition Width="2" />
                    <ColumnDefinition Width="3*" MinWidth="250" />
                </Grid.ColumnDefinitions>

                <!-- Left Column (TabControl_MS2view) -->
                <Grid Name="Grid_Left" Grid.Column="0">
                    <Grid Name="Grid_MSview">
                        <TabControl Name="TabControl_MS2view" SelectedIndex="{Binding TabMassSpectraView, Converter={StaticResource TabMassSpectraViewIntConverter}}">
                            <i:Interaction.Triggers>
                                <i:EventTrigger EventName="SelectionChanged">
                                    <i:InvokeCommandAction Command="{Binding Path=SelectionChangedTabControlMsTableCommand}"/>
                                </i:EventTrigger>
                            </i:Interaction.Triggers>
                            <TabItem Header="MS/MS single viewer" Name="TabItem_SingleView" IsSelected="True">
                                <view:MassSpectrumUI x:Name="SelectedSpectrumUI" Content="{Binding SingleMassSpectrumUI}" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="0,2,2,0">
                                    <view:MassSpectrumUI.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Save spectrum" Command="{Binding SaveChart}" CommandParameter="{Binding SingleMassSpectrumVM}" />
                                        </ContextMenu>
                                    </view:MassSpectrumUI.ContextMenu>
                                </view:MassSpectrumUI>
                            </TabItem>
                            <TabItem Header="MS/MS multiple viewer" Name="TabItem_ScrollView">
                                <ScrollViewer Name="MultiSpectra" Content="{Binding MultipleSpectra}" VerticalScrollBarVisibility="Auto" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="0,5,1,0"/>
                            </TabItem>
                            <TabItem Header="MS/MS grouping viewer" Name="TabItem_GroupView">
                                <view:MassSpectrumUI x:Name="ConsensusSpectrumUI" Content="{Binding ConsensusSpectrumUI}" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Margin="0,2,2,0">
                                    <view:MassSpectrumUI.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="Save spectrum" Command="{Binding SaveChart}" CommandParameter="{Binding ConsensusSpectrumVM}" />
                                        </ContextMenu>
                                    </view:MassSpectrumUI.ContextMenu>
                                </view:MassSpectrumUI>
                            </TabItem>
                        </TabControl>
                    </Grid>

                </Grid>

                <!-- Center Column (Tab_MS_Table) -->
                <Grid Name="Grid_Center" Grid.Column="2">
                    <TabControl Name="Tab_MS_Table" SelectedIndex="{Binding TabMassSpectrumTable, Converter={StaticResource TabMassSpectrumTableIntConverter}}" Margin="0,0,0,0">
                        <TabItem Header="Peak Information" Name="TabItem_SingleMS">
                            <DataGrid Name="DataGrid_SingleMassSpectrumTable"
                                     ItemsSource="{Binding SelectedSpectrum.Spectrum}"
                                     SelectedItem="{Binding Path=SelectedPeak}"
                                     CanUserAddRows="True"
                                     CanUserDeleteRows="True"
                                     CanUserReorderColumns="False"
                                     CanUserSortColumns="True"
                                     SelectionUnit="FullRow"
                                     IsReadOnly="False"
                                     HeadersVisibility="All"
                                     AutoGenerateColumns="False"
                                     AlternationCount="2"
                                     Style="{StaticResource ModernDataGrid}"
                                     RowStyle="{StaticResource ModernDataGridRow}">
                                <i:Interaction.Triggers>
                                    <i:EventTrigger EventName="SelectionChanged">
                                        <i:InvokeCommandAction Command="{Binding Path=SelectionChangedSingleSpectrumTableCommand}" />
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>
                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource ModernDataGridColumnHeader}">
                                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                        <Setter Property="Padding" Value="7,4,0,4"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Mz" Width="1.2*" Binding="{Binding Path=Mz, StringFormat=0.0000, UpdateSourceTrigger=LostFocus}" IsReadOnly="False" />
                                    <DataGridTextColumn Header="Intensity" Width="1.7*" Binding="{Binding Path=Intensity, StringFormat=0.0, UpdateSourceTrigger=LostFocus}" IsReadOnly="False" />
                                    <DataGridTextColumn Header="Comment" Width="3.5*" Binding="{Binding Path=Comment, UpdateSourceTrigger=LostFocus}" IsReadOnly="False" />
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                        <TabItem Header="Consensus Peak"
                                Name="TabItem_ConsensusMS">
                            <DataGrid Name="DataGrid_Consensus"
                                    ItemsSource="{Binding ConsensusSpectraTable}"
                                    SelectedItem="{Binding SelectedMsGroup}"
                                    CanUserAddRows="True"
                                      CanUserDeleteRows="True"
                                    CanUserReorderColumns="False"
                                    CanUserSortColumns="True"
                                    SelectionUnit="FullRow"
                                    IsReadOnly="False"
                                    HeadersVisibility="All"
                                    AutoGenerateColumns="False"
                                    VerticalScrollBarVisibility="Visible"
                                    ScrollViewer.HorizontalScrollBarVisibility="Auto"
                                    AlternationCount="2"
                                    Style="{StaticResource ModernDataGrid}"
                                    RowStyle="{StaticResource ModernDataGridRow}">
                                <i:Interaction.Triggers>
                                    <i:EventTrigger EventName="SelectionChanged">
                                        <i:InvokeCommandAction Command="{Binding Path=SelectionChangedConsensusTableCommand}"/>
                                    </i:EventTrigger>
                                </i:Interaction.Triggers>

                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource ModernDataGridColumnHeader}">
                                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                        <Setter Property="Padding" Value="7,4,0,4"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Mz"
                                            Width="Auto"
                                            Binding="{Binding Path=MedianMz, StringFormat=0.0000}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="Intensity"
                                            Width="Auto"
                                            Binding="{Binding Path=MedianIntensity, StringFormat=0.0}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="Counter"
                                            Width="Auto"
                                            Binding="{Binding Path=Counter, StringFormat=0}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="MinMz"
                                            Width="Auto"
                                            Binding="{Binding Path=MinMz, StringFormat=0.0000}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="MaxMz"
                                            Width="Auto"
                                            Binding="{Binding Path=MaxMz, StringFormat=0.0000}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="MinInt"
                                            Width="Auto"
                                            Binding="{Binding Path=MinIntensity, StringFormat=0.0}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="MaxInt"
                                            Width="Auto"
                                            Binding="{Binding Path=MaxIntensity, StringFormat=0.0}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="Rep.Formula"
                                            Width="Auto"
                                            Binding="{Binding Path=CommonFORMULA.Formula }"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="Rep.Smiles"
                                            Width="Auto"
                                            Binding="{Binding Path=CommonSMILES.Smiles }"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="NumFormula"
                                            Width="Auto"
                                            Binding="{Binding Path=Formula.Count, StringFormat=0}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="NumSmiles"
                                            Width="Auto"
                                            Binding="{Binding Path=SMILES.Count, StringFormat=0}"
                                            IsReadOnly="True"/>
                                    <DataGridTextColumn Header="Comment"
                                            Width="Auto"
                                            Binding="{Binding Path=CommonSMILES.Formula}"
                                            IsReadOnly="False"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </TabItem>
                    </TabControl>
                </Grid>

                <!-- Splitters between columns -->
                <GridSplitter Grid.Column="1" Width="2" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" ResizeBehavior="PreviousAndNext" />
                <GridSplitter Grid.Column="3" Width="2" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" ResizeBehavior="PreviousAndNext" />

                <!-- Right Column (DataGrid_Spectra) -->
                <Grid Name="Grid_Right" Grid.Column="4">
                    <Label Content="Spectra" FontStyle="Normal" Height="39" VerticalAlignment="Top" HorizontalContentAlignment="Center" Background="Transparent"/>
                    <DataGrid Name="DataGrid_Spectra" ItemsSource="{Binding SelectedCompoundBean.Spectra}" SelectedItem="{Binding Path=SelectedSpectrum}"
                             CanUserAddRows="False" CanUserReorderColumns="False" CanUserSortColumns="True" SelectionUnit="FullRow"
                             HeadersVisibility="All" AutoGenerateColumns="False" Margin="0,39,0,0" AlternationCount="2" Style="{StaticResource ModernDataGrid}"
                             RowStyle="{StaticResource ModernDataGridRow}"
                             ScrollViewer.HorizontalScrollBarVisibility="Auto" ScrollViewer.VerticalScrollBarVisibility="Auto"
                             ScrollViewer.CanContentScroll="False" ScrollViewer.PanningMode="Both" ScrollViewer.PanningDeceleration="0.001"
                             ScrollViewer.PanningRatio="1" IsManipulationEnabled="True" ManipulationBoundaryFeedback="DataGrid_ManipulationBoundaryFeedback">
                        <DataGrid.ColumnHeaderStyle>
                            <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource ModernDataGridColumnHeader}">
                                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                <Setter Property="Padding" Value="7,4,0,4"/>
                            </Style>
                        </DataGrid.ColumnHeaderStyle>
                        <DataGrid.CellStyle>
                            <Style TargetType="DataGridCell" BasedOn="{StaticResource {x:Type DataGridCell}}">
                                <Setter Property="Padding" Value="7,4,0,4"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="DataGridCell">
                                            <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </DataGrid.CellStyle>
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Width="50" Binding="{Binding Path=Id}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Name" Width="200" Binding="{Binding Path=Name}" IsReadOnly="False"/>
                            <DataGridTextColumn Header="Retention time" Width="115" Binding="{Binding Path=RetentionTime, StringFormat=0.00}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Mz" Width="90" Binding="{Binding Path=PrecursorMz, StringFormat=0.0000}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Mz shift (ppm)" Width="120" Binding="{Binding Path=DiffPpm, StringFormat=0.0}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Adduct" Width="85" Binding="{Binding Path=AdductIon.AdductIonName}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Collision Energy" Width="130" Binding="{Binding Path=CollisionEnergy, StringFormat=0.0}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Num Peaks" Width="100" Binding="{Binding Path=PeakNumber}" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Comment" Width="180" Binding="{Binding Path=Comment}" IsReadOnly="False"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Grid>

            <!-- Row splitter between top and bottom sections -->
            <GridSplitter Grid.Row="1" Height="2" HorizontalAlignment="Stretch" VerticalAlignment="Top" ResizeBehavior="PreviousAndNext" />

            <!-- Bottom Section -->
            <Grid Name="Grid_Bottom" Grid.Row="2" Grid.ColumnSpan="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2.5*" MinWidth="320" />
                    <ColumnDefinition Width="2" />
                    <ColumnDefinition Width="7.5*" MinWidth="400" />
                </Grid.ColumnDefinitions>

                <!-- Left side - Compound Information -->
                <Grid Grid.Column="0">
                    <Label Content="General Information" FontStyle="Normal" Height="35" VerticalAlignment="Top" HorizontalContentAlignment="Center" Background="Transparent"/>
                    <Grid Name="Grid_RawData_Info" Margin="5,35,5,0" VerticalAlignment="Stretch">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>


                        <!-- Top section with info and filters -->
                        <StackPanel Orientation="Vertical" Grid.Row="0" Grid.Column="0" Margin="0,0,0,5" VerticalAlignment="Top">
                            <TextBox Name="Label_NumComp" Text="{Binding LabelNumCompounds, Mode=OneWay}" Margin="0,0,0,1" FontFamily="Calibri" BorderThickness="0" IsReadOnly="True" FontSize="{DynamicResource BodyFontSize}" Height="18" Padding="2,1"/>
                            <TextBox Name="Label_NumSpectra" Text="{Binding LabelNumSpectra, Mode=OneWay}" Margin="0,0,0,1" FontFamily="Calibri" BorderThickness="0" IsReadOnly="True" FontSize="{DynamicResource BodyFontSize}" Height="18" Padding="2,1"/>
                            <TextBox Name="Label_SelectedComp" Text="{Binding LabelSelectedCompound, Mode=OneWay}" Margin="0,0,0,1" FontFamily="Calibri" BorderThickness="0" IsReadOnly="True" FontSize="{DynamicResource BodyFontSize}" Height="18" Padding="2,1"/>
                            <TextBox Name="Label_SelectedSpectra" Text="{Binding LabelSelectedSpectra, Mode=OneWay}" Margin="0,0,0,1" FontFamily="Calibri" BorderThickness="0" IsReadOnly="True" FontSize="{DynamicResource BodyFontSize}" Height="18" Padding="2,1"/>
                        </StackPanel>

                        <!-- Filter controls removed as per UI redesign -->

                        <!-- Structure display area -->
                        <Grid Grid.Row="2" Grid.Column="0" VerticalAlignment="Stretch" HorizontalAlignment="Stretch" Margin="0,5,0,0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Structure Image with Interactive Zoom and Pan Support -->
                            <Border Grid.Row="0"
                                    BorderThickness="1"
                                    BorderBrush="{DynamicResource BorderBrush}"
                                    Background="{DynamicResource SurfaceBrush}"
                                    CornerRadius="{DynamicResource DefaultCornerRadius}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch">
                                <ScrollViewer Name="StructureScrollViewer"
                                              HorizontalScrollBarVisibility="Auto"
                                              VerticalScrollBarVisibility="Auto"
                                              PanningMode="Both"
                                              HorizontalContentAlignment="Left"
                                              VerticalContentAlignment="Top"
                                              HorizontalAlignment="Stretch"
                                              VerticalAlignment="Stretch"
                                              CanContentScroll="False"
                                              PreviewMouseWheel="StructureScrollViewer_PreviewMouseWheel"
                                              PreviewMouseLeftButtonDown="StructureScrollViewer_PreviewMouseLeftButtonDown"
                                              PreviewMouseMove="StructureScrollViewer_PreviewMouseMove"
                                              PreviewMouseLeftButtonUp="StructureScrollViewer_PreviewMouseLeftButtonUp"
                                              ManipulationStarting="StructureScrollViewer_ManipulationStarting"
                                              ManipulationDelta="StructureScrollViewer_ManipulationDelta"
                                              ManipulationCompleted="StructureScrollViewer_ManipulationCompleted"
                                              ManipulationBoundaryFeedback="StructureScrollViewer_ManipulationBoundaryFeedback"
                                              IsManipulationEnabled="True">
                                    <Canvas Name="StructureContainer"
                                            Background="Transparent">
                                        <Image Name="StructureImage"
                                               Source="{Binding StructureImage, NotifyOnSourceUpdated=True, UpdateSourceTrigger=PropertyChanged}"
                                               Stretch="None"
                                               RenderOptions.BitmapScalingMode="HighQuality"
                                               SnapsToDevicePixels="True"
                                               SourceUpdated="StructureImage_SourceUpdated"
                                               Canvas.Left="0"
                                               Canvas.Top="0">
                                            <Image.RenderTransform>
                                                <ScaleTransform x:Name="StructureImageTransform" ScaleX="1.0" ScaleY="1.0"/>
                                            </Image.RenderTransform>
                                        </Image>
                                    </Canvas>
                                </ScrollViewer>
                            </Border>

                            <!-- Enhanced Zoom Controls -->
                            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5,0,0">
                                <Button Name="ZoomOutButton" Content="−" Width="35" Height="30" Margin="2,0" Click="ZoomOutButton_Click" ToolTip="Zoom Out (Ctrl + Mouse Wheel)" FontSize="16" FontWeight="Bold" Background="{DynamicResource DataGridHeaderGradientBrush}" Foreground="{DynamicResource PrimaryTextBrush}" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Padding="0,-2,0,0"/>
                                <Button Name="ZoomFitButton" Content="Reset" Width="65" Height="30" Margin="2,0" Click="ZoomFitButton_Click" ToolTip="Reset and Fit to Window" FontSize="11" Background="{DynamicResource DataGridHeaderGradientBrush}" Foreground="{DynamicResource PrimaryTextBrush}" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"/>
                                <Button Name="ZoomInButton" Content="+" Width="35" Height="30" Margin="2,0" Click="ZoomInButton_Click" ToolTip="Zoom In (Ctrl + Mouse Wheel)" FontSize="16" FontWeight="Bold" Background="{DynamicResource DataGridHeaderGradientBrush}" Foreground="{DynamicResource PrimaryTextBrush}" HorizontalContentAlignment="Center" VerticalContentAlignment="Center" Padding="0,-2,0,0"/>
                                <TextBlock Name="ZoomLevelText"
                                           Text="100%"
                                           VerticalAlignment="Center"
                                           Margin="10,0,0,0"
                                           FontSize="{DynamicResource CaptionFontSize}"
                                           Foreground="{DynamicResource SecondaryTextBrush}"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Grid>

                <!-- Splitter between General Info and Compound Table -->
                <GridSplitter Grid.Column="1" Width="2" HorizontalAlignment="Stretch" VerticalAlignment="Stretch" ResizeBehavior="PreviousAndNext" />

                <!-- Right side - Compound table -->
                <Grid Grid.Column="2">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="35"/>
                        <RowDefinition Height="32"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header row with Clear button on left and title centered -->
                    <Grid Grid.Row="0" Height="35" Background="Transparent">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Clear All Filters Button - moved to left side with theme-specific hex colors -->
                        <Button Grid.Column="0" Content="Clear All Filters" Command="{Binding ClearAllFiltersCommand}"
                                                        Width="120" Height="28" Margin="10,0,15,0" VerticalAlignment="Center" HorizontalAlignment="Left"
                                                        FontSize="11" FontWeight="Medium" HorizontalContentAlignment="Center" VerticalContentAlignment="Center"
                                                        Foreground="{DynamicResource SecondaryTextBrush}"
                                                        BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1"
                                                        ToolTip="Clear all filter inputs"
                                                        Cursor="Hand" Padding="8,4,8,4">
                                                    <Button.Style>
                                                        <Style TargetType="Button">
                                                            <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
                                                            <Setter Property="Foreground" Value="{DynamicResource SecondaryTextBrush}"/>
                                                            <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
                                                            <Setter Property="BorderThickness" Value="1"/>
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="Button">
                                                                        <Border x:Name="Border"
                                                                                Background="{TemplateBinding Background}"
                                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                                                CornerRadius="4"
                                                                                SnapsToDevicePixels="True">
                                                                            <ContentPresenter x:Name="ContentPresenter"
                                                                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                                                              Margin="{TemplateBinding Padding}"
                                                                                              TextElement.Foreground="{TemplateBinding Foreground}"/>
                                                                        </Border>
                                                                        <ControlTemplate.Triggers>
                                                                            <!-- Hover Effects with theme-specific base colors -->
                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                <Setter TargetName="Border" Property="Effect">
                                                                                    <Setter.Value>
                                                                                        <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                                                                    </Setter.Value>
                                                                                </Setter>
                                                                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                                                                                <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="White"/>
                                                                            </Trigger>
                                                                            <!-- Pressed Effects with enhanced styling -->
                                                                            <Trigger Property="IsPressed" Value="True">
                                                                                <Setter TargetName="Border" Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                                                                                <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="White"/>
                                                                                <Setter TargetName="Border" Property="Effect">
                                                                                    <Setter.Value>
                                                                                        <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="4" ShadowDepth="1"/>
                                                                                    </Setter.Value>
                                                                                </Setter>
                                                                            </Trigger>
                                                                            <!-- Disabled state with theme-specific base colors -->
                                                                            <Trigger Property="IsEnabled" Value="False">
                                                                                <Setter TargetName="Border" Property="Opacity" Value="0.5"/>
                                                                                <Setter TargetName="ContentPresenter" Property="TextElement.Foreground" Value="{DynamicResource SecondaryTextBrush}"/>
                                                                            </Trigger>
                                                                        </ControlTemplate.Triggers>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </Button.Style>
                                                </Button>

                        <!-- Compounds Information Label - centered in remaining space -->
                        <Label Grid.Column="1" Content="Compounds Information" FontStyle="Normal"
                               VerticalAlignment="Center" HorizontalContentAlignment="Center" Background="Transparent"/>
                    </Grid>

                    <!-- SYNCHRONIZED FILTER ROW - Bidirectional horizontal scrolling with DataGrid -->
                    <Grid Grid.Row="1" Name="FilterRow" Background="{DynamicResource TableHeaderBrush}" Height="32">
                        <ScrollViewer Name="FilterScrollViewer" HorizontalScrollBarVisibility="Hidden" VerticalScrollBarVisibility="Hidden"
                                      CanContentScroll="True" PanningMode="HorizontalOnly" ScrollChanged="FilterScrollViewer_ScrollChanged">
                            <!-- Filter Grid with 5px left margin for perfect alignment with DataGrid columns -->
                            <!-- Dynamic filter grid - populated by DynamicColumnManager -->
                            <Grid Name="FilterGrid" HorizontalAlignment="Left" Margin="5,0,0,0">
                                <!-- Column definitions and filter controls will be dynamically generated -->
                            </Grid>
                        </ScrollViewer>
                    </Grid>

                    <!-- HIGH-PERFORMANCE DataGrid with VIRTUALIZATION ENABLED for large datasets -->
                    <DataGrid Name="DataGrid_CompoundTable" Grid.Row="2"
                                     ItemsSource="{Binding FilteredCompoundTableView}" SelectedItem="{Binding Path=SelectedCompoundBean}"
                                     CanUserAddRows="False" CanUserReorderColumns="False" CanUserSortColumns="True"
                                     SelectionUnit="FullRow" HeadersVisibility="All" AutoGenerateColumns="False"
                                     ScrollViewer.HorizontalScrollBarVisibility="Auto" ScrollViewer.VerticalScrollBarVisibility="Auto"
                                     ScrollViewer.CanContentScroll="True" ScrollViewer.PanningMode="Both" ScrollViewer.PanningDeceleration="0.001"
                                     ScrollViewer.PanningRatio="1" IsManipulationEnabled="True" ManipulationBoundaryFeedback="DataGrid_ManipulationBoundaryFeedback"
                                     HorizontalAlignment="Stretch" ColumnReordered="DataGrid_CompoundTable_ColumnReordered"
                                     ColumnDisplayIndexChanged="DataGrid_CompoundTable_ColumnDisplayIndexChanged"
                                     Loaded="DataGrid_CompoundTable_Loaded"
                                     Margin="0,0,0,0" AlternationCount="2" Style="{StaticResource ModernDataGrid}"
                                     RowStyle="{StaticResource ModernDataGridRow}"
                                     VirtualizingPanel.IsVirtualizing="True"
                                     VirtualizingPanel.VirtualizationMode="Recycling"
                                     VirtualizingPanel.IsContainerVirtualizable="True"
                                     EnableRowVirtualization="True"
                                     EnableColumnVirtualization="True"
                                     ScrollViewer.ScrollChanged="DataGrid_CompoundTable_ScrollChanged">
                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader" BasedOn="{StaticResource ModernDataGridColumnHeader}">
                                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                                    <Setter Property="Padding" Value="7,4,0,4"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>
                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell" BasedOn="{StaticResource {x:Type DataGridCell}}">
                                    <Setter Property="Padding" Value="7,4,0,4"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="DataGridCell">
                                                <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </DataGrid.CellStyle>
                            <DataGrid.Columns>
                                <!-- Static columns with dynamic visibility control -->

                                <!-- 1. ID -->
                                <DataGridTextColumn Header="ID" Width="60" Binding="{Binding Path=Id}" IsReadOnly="True"/>
                                <!-- 2. Name -->
                                <DataGridTextColumn Header="Name" Width="200" Binding="{Binding Path=Name, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Name}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 3. Retention time -->
                                <DataGridTextColumn Header="Retention time" Width="120" Binding="{Binding Path=RetentionTimes, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 4. Precursor MZ -->
                                <DataGridTextColumn Header="Precursor MZ" Width="110" Binding="{Binding Path=PrecursorMz, StringFormat=0.0000, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 5. Precursor type -->
                                <DataGridTextColumn Header="Precursor type" Width="120" Binding="{Binding Path=PrecursorType, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=PrecursorType}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 6. Ion mode -->
                                <DataGridTextColumn Header="Ion mode" Width="100" Binding="{Binding Path=IonMode, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 7. CAS -->
                                <DataGridTextColumn Header="CAS" Width="120" Binding="{Binding Path=CAS, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=CAS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 8. CID -->
                                <DataGridTextColumn Header="CID" Width="80" Binding="{Binding Path=CID, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 9. Formula -->
                                <DataGridTextColumn Header="Formula" Width="120" Binding="{Binding Path=Formula, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Formula}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 10. Exact mass -->
                                <DataGridTextColumn Header="Exact mass" Width="100" Binding="{Binding Path=ExactMass, StringFormat=0.0000, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>

                                <!-- 11. Collision energy -->
                                <DataGridTextColumn Header="Collision energy" Width="120" Binding="{Binding Path=CollisionEnergy, StringFormat=0.0}" IsReadOnly="False"/>
                                <!-- 12. Num peaks -->
                                <DataGridTextColumn Header="Num peaks" Width="100" Binding="{Binding Path=Num_peaks}" IsReadOnly="False"/>

                                <!-- 13-27. Toxicological/Regulatory Columns -->
                                <!-- 13. Cramer rules -->
                                <DataGridTextColumn Header="Cramer rules" Width="100" Binding="{Binding Path=Cramerrules, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 14. SVHC -->
                                <DataGridTextColumn Header="SVHC" Width="80" Binding="{Binding Path=SVHC, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 15. CMR -->
                                <DataGridTextColumn Header="CMR" Width="80" Binding="{Binding Path=CMR, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 16. CMR suspect -->
                                <DataGridTextColumn Header="CMR suspect" Width="100" Binding="{Binding Path=CMRSuspect, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 17. EDC -->
                                <DataGridTextColumn Header="EDC" Width="80" Binding="{Binding Path=EDC, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 18. IARC -->
                                <DataGridTextColumn Header="IARC" Width="80" Binding="{Binding Path=IARC, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 19. EU SML -->
                                <DataGridTextColumn Header="EU SML" Width="100" Binding="{Binding Path=Eusml, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 20. China SML -->
                                <DataGridTextColumn Header="China SML" Width="100" Binding="{Binding Path=ChinaSml, Converter={StaticResource NullToSlashConverter}}" IsReadOnly="False"/>
                                <!-- 21. Carcinogenicity ISS -->
                                <DataGridTextColumn Header="Carcinogenicity ISS" Width="150" Binding="{Binding Path=Carcinogenicity_ISS}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Carcinogenicity_ISS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 22. DNA alerts OASIS -->
                                <DataGridTextColumn Header="DNA alerts OASIS" Width="150" Binding="{Binding Path=DNA_alerts_OASIS}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=DNA_alerts_OASIS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 23. DNA binding OASIS -->
                                <DataGridTextColumn Header="DNA binding OASIS" Width="150" Binding="{Binding Path=DNA_binding_OASIS}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=DNA_binding_OASIS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 24. DNA binding OECD -->
                                <DataGridTextColumn Header="DNA binding OECD" Width="150" Binding="{Binding Path=DNA_binding_OECD}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=DNA_binding_OECD}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 25. Protein binding alerts OASIS -->
                                <DataGridTextColumn Header="Protein binding alerts OASIS" Width="200" Binding="{Binding Path=Protein_binding_alerts_OASIS}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Protein_binding_alerts_OASIS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 26. Vitro mutagenicity alerts ISS -->
                                <DataGridTextColumn Header="Vitro mutagenicity alerts ISS" Width="200" Binding="{Binding Path=Vitro_mutagenicity_alerts_ISS}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Vitro_mutagenicity_alerts_ISS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 27. Vivo mutagenicity alerts ISS -->
                                <DataGridTextColumn Header="Vivo mutagenicity alerts ISS" Width="200" Binding="{Binding Path=Vivo_mutagenicity_alerts_ISS}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Vivo_mutagenicity_alerts_ISS}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 28-32. Chemical Structure Columns -->
                                <!-- 28. SMILES -->
                                <DataGridTextColumn Header="SMILES" Width="300" Binding="{Binding Path=Smiles}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Smiles}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 29. InChI -->
                                <DataGridTextColumn Header="InChI" Width="300" Binding="{Binding Path=InChI}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=InChI}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 30. InChI key -->
                                <DataGridTextColumn Header="InChIKey" Width="300" Binding="{Binding Path=InChIKey}" IsReadOnly="True">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=InChIKey}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 31. IUPAC name -->
                                <DataGridTextColumn Header="IUPAC name" Width="300" Binding="{Binding Path=IUPACName}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=IUPACName}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 32-36. Classification Columns -->
                                <!-- 32. Superclass -->
                                <DataGridTextColumn Header="Superclass" Width="150" Binding="{Binding Path=Superclass}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Superclass}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 33. Class -->
                                <DataGridTextColumn Header="Class" Width="150" Binding="{Binding Path=Class}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Class}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 34. Subclass -->
                                <DataGridTextColumn Header="Subclass" Width="150" Binding="{Binding Path=Subclass}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Subclass}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 35. Ontology -->
                                <DataGridTextColumn Header="Ontology" Width="150" Binding="{Binding Path=Ontology}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Ontology}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 36-42. Physicochemical Properties -->
                                <!-- 36. XLogP -->
                                <DataGridTextColumn Header="XLogP" Width="80" Binding="{Binding Path=XLogP, StringFormat=0.00}" IsReadOnly="False"/>
                                <!-- 37. MLogP -->
                                <DataGridTextColumn Header="MLogP" Width="80" Binding="{Binding Path=MLogP, StringFormat=0.00}" IsReadOnly="False"/>
                                <!-- 38. ALogP -->
                                <DataGridTextColumn Header="ALogP" Width="80" Binding="{Binding Path=ALogP, StringFormat=0.00}" IsReadOnly="False"/>
                                <!-- 39. Topo PSA -->
                                <DataGridTextColumn Header="Topo PSA" Width="100" Binding="{Binding Path=TopoPSA, StringFormat=0.00}" IsReadOnly="False"/>
                                <!-- 40. Num bonds -->
                                <DataGridTextColumn Header="Num bonds" Width="100" Binding="{Binding Path=NumBond}" IsReadOnly="False"/>
                                <!-- 41. Num atoms -->
                                <DataGridTextColumn Header="Num atoms" Width="100" Binding="{Binding Path=NumAtom}" IsReadOnly="False"/>

                                <!-- 42-45. Metadata Columns -->
                                <!-- 42. Authors -->
                                <DataGridTextColumn Header="Authors" Width="150" Binding="{Binding Path=Authors}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Authors}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 43. Instrument -->
                                <DataGridTextColumn Header="Instrument" Width="150" Binding="{Binding Path=Instrument}" IsReadOnly="False">
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="ToolTip" Value="{Binding Path=Instrument}"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                                <!-- 44. Instrument type -->
                                <DataGridTextColumn Header="Instrument type" Width="120" Binding="{Binding Path=InstrumentType}" IsReadOnly="False"/>
                                <!-- 45. Date -->
                                <DataGridTextColumn Header="Date" Width="100" Binding="{Binding Path=Date}" IsReadOnly="False"/>
                            </DataGrid.Columns>
                        </DataGrid>
                </Grid>
            </Grid>
        </Grid>
    </DockPanel>
</Window>
