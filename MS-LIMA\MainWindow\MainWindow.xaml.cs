﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Metabolomics.MsLima.Bean;
using Metabolomics.MsLima.Model;
using Metabolomics.MsLima.Services;
using Metabolomics.MsLima.Utility;
using ChartDrawing.Services;
using Metabolomics.Core.Handler;
using Metabolomics.Core;
using Microsoft.Win32;

namespace Metabolomics.MsLima {

    /// <summary>
    /// MainWindow.xaml の相互作用ロジック
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        // OPTIMIZATION: Flag to defer structure image refresh during theme changes
        private bool _structureImageNeedsRefresh = false;
        public event PropertyChangedEventHandler PropertyChanged;

        private bool _isDarkTheme;
        public bool IsDarkTheme
        {
            get { return _isDarkTheme; }
            set
            {
                if (_isDarkTheme != value)
                {
                    _isDarkTheme = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsDarkTheme)));
                }
            }
        }

        private ColorScheme _currentColorScheme = ColorScheme.MaterialBlue;
        public ColorScheme CurrentColorScheme
        {
            get { return _currentColorScheme; }
            set
            {
                if (_currentColorScheme != value)
                {
                    _currentColorScheme = value;
                    PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(CurrentColorScheme)));
                }
            }
        }
        // Enhanced zoom and pan properties for the structure image
        private double _zoomFactor = 1.0;
        private const double _zoomIncrement = 0.2;
        private const double _minZoom = 0.1; // Allow significant zoom out
        private const double _maxZoom = 10.0; // Allow significant zoom in
        private const double _wheelZoomFactor = 1.2; // Smooth wheel zoom
        private ScaleTransform _structureImageTransform; // Will be set from XAML
        private Point _lastCenterPosition;
        private bool _isFirstStructureLoad = true;

        // Pan functionality properties
        private bool _isPanning = false;
        private Point _panStartPoint;
        private Point _panStartOffset;

        // Touch/manipulation properties
        private bool _isManipulating = false;
        private double _initialZoom = 1.0;
        private Point _manipulationStartCenter;
        private double _cumulativeZoomFactor = 1.0;

        // Custom inertia properties for smooth touch experience
        private DateTime _lastManipulationTime;
        private Point _lastManipulationPosition;
        private Vector _manipulationVelocity;
        private System.Windows.Threading.DispatcherTimer _inertiaTimer;
        private bool _isFirstManipulationDelta;

        public MainWindow()
        {
            InitializeComponent();
            this.DataContext = new MainWindowVM();

            // Initialize theme system
            InitializeThemeSystem();

            // Initialize the structure image zoom transform
            this.Loaded += MainWindow_Loaded;

            // Add keyboard event handlers for navigation
            this.KeyDown += MainWindow_KeyDown;

            // Initialize custom inertia timer for smooth touch experience
            InitializeInertiaTimer();

            // ENHANCED: Add event handlers for scroll synchronization edge cases
            this.SizeChanged += MainWindow_SizeChanged;

            // Subscribe to theme changes for scroll synchronization maintenance
            if (ThemeService.Instance != null)
            {
                ThemeService.Instance.ThemeChanged += ThemeService_ThemeChanged;
            }

            // Auto-import database on startup - TEMPORARILY DISABLED FOR TESTING
            // this.Loaded += MainWindow_AutoImportDatabase;
        }

        /// <summary>
        /// Initializes the theme system and subscribes to theme changes
        /// </summary>
        private void InitializeThemeSystem()
        {
            // Register the theme service with the provider service
            ThemeProviderService.SetProvider(ThemeService.Instance);

            // Subscribe to theme manager changes
            ThemeManager.Instance.ThemeChanged += OnThemeManagerChanged;

            // Subscribe to theme service changes for dual controls
            ThemeService.Instance.BaseThemeChanged += OnBaseThemeChanged;
            ThemeService.Instance.ColorSchemeChanged += OnColorSchemeChanged;

            // Initialize current theme state
            IsDarkTheme = ThemeManager.Instance.IsDarkTheme;
            CurrentColorScheme = ThemeManager.Instance.CurrentColorScheme;
            ThemeService.Instance.IsDarkTheme = ThemeManager.Instance.IsDarkTheme;
            ThemeService.Instance.CurrentColorScheme = ThemeManager.Instance.CurrentColorScheme;

            // Set up theme detection for SmilesConverter
            Metabolomics.Core.SmilesConverter.IsDarkThemeFunc = () => ThemeService.Instance.IsDarkTheme;
        }

        /// <summary>
        /// Handle base theme changes (light/dark)
        /// </summary>
        private void OnBaseThemeChanged(object sender, BaseTheme newBaseTheme)
        {
            var isDarkTheme = newBaseTheme == BaseTheme.Dark;
            IsDarkTheme = isDarkTheme;

            // TARGETED APPROACH: Only refresh the 2 specific components that need manual theme updates
            // This maintains <50ms performance while fixing visual regression
            RefreshSpectrumPlotsAndStructureImageForTheme();

            System.Diagnostics.Debug.WriteLine($"Base theme changed to {newBaseTheme} - targeted component refresh completed");
        }

        /// <summary>
        /// Handle color scheme changes
        /// </summary>
        private void OnColorSchemeChanged(object sender, ColorScheme newColorScheme)
        {
            CurrentColorScheme = newColorScheme;

            // Force immediate refresh of all color-dependent UI components
            RefreshSpectrumPlotsAndStructureImageForTheme();

            // Force refresh of any data-bound UI elements
            this.InvalidateVisual();

            // Update theme manager to keep everything in sync
            ThemeManager.Instance.CurrentColorScheme = newColorScheme;

            System.Diagnostics.Debug.WriteLine($"Color scheme changed to {newColorScheme} - all UI components refreshed");
        }

        /// <summary>
        /// ULTRA-FAST theme change handler with TARGETED visual fixes for spectrum plots and structure images (backward compatibility)
        /// </summary>
        private void OnThemeManagerChanged(object sender, bool isDarkTheme)
        {
            IsDarkTheme = isDarkTheme;
            ThemeService.Instance.IsDarkTheme = isDarkTheme;

            // TARGETED APPROACH: Only refresh the 2 specific components that need manual theme updates
            // This maintains <50ms performance while fixing visual regression
            RefreshSpectrumPlotsAndStructureImageForTheme();

            System.Diagnostics.Debug.WriteLine($"Theme changed to {(isDarkTheme ? "Dark" : "Light")} - targeted component refresh completed");
        }

        /// <summary>
        /// ULTRA-FAST theme toggle handler - optimized for <50ms completion regardless of data size
        /// </summary>
        private async void ThemeToggleControl_ThemeChanged(object sender, bool isDarkTheme)
        {
            var themeStopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                System.Diagnostics.Debug.WriteLine($"ULTRA-FAST theme toggle: {(isDarkTheme ? "Dark" : "Light")} theme");

                // Update theme services immediately for responsive UI
                ThemeService.Instance.IsDarkTheme = isDarkTheme;

                // Apply theme asynchronously to prevent UI blocking
                await ThemeManager.Instance.SetThemeAsync(isDarkTheme);

                // REVOLUTIONARY OPTIMIZATION: NO mass spectrum refreshing during theme change
                // WPF resource binding automatically handles theme updates for all components
                // This eliminates ALL performance bottlenecks with large datasets

                themeStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"ULTRA-FAST theme toggle completed in {themeStopwatch.ElapsedMilliseconds}ms: {(isDarkTheme ? "Dark" : "Light")} theme");

                if (themeStopwatch.ElapsedMilliseconds > 50)
                {
                    System.Diagnostics.Debug.WriteLine($"PERFORMANCE WARNING: Theme toggle took {themeStopwatch.ElapsedMilliseconds}ms - target is <50ms");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR in theme toggle handler: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handle color scheme selector changes
        /// </summary>
        private async void ColorSchemeSelectorControl_ColorSchemeChanged(object sender, ColorScheme selectedColorScheme)
        {
            var colorSchemeStopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                System.Diagnostics.Debug.WriteLine($"Color scheme selector: switching to {ThemeService.GetColorSchemeDisplayName(selectedColorScheme)}");

                // Update theme service immediately for responsive UI
                ThemeService.Instance.CurrentColorScheme = selectedColorScheme;

                // Force immediate UI refresh
                RefreshSpectrumPlotsAndStructureImageForTheme();
                this.InvalidateVisual();

                // Small delay to ensure UI updates are processed
                await Task.Delay(25);

                colorSchemeStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"Color scheme selector completed in {colorSchemeStopwatch.ElapsedMilliseconds}ms");

                if (colorSchemeStopwatch.ElapsedMilliseconds > 50)
                {
                    System.Diagnostics.Debug.WriteLine($"PERFORMANCE WARNING: Color scheme change took {colorSchemeStopwatch.ElapsedMilliseconds}ms - target is <50ms");
                }
            }
            catch (Exception ex)
            {
                colorSchemeStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR in color scheme selector: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// TARGETED refresh for ONLY spectrum plots and structure images - maintains <50ms performance
        /// </summary>
        private void RefreshSpectrumPlotsAndStructureImageForTheme()
        {
            try
            {
                var themeStopwatch = System.Diagnostics.Stopwatch.StartNew();
                var viewModel = this.DataContext as MainWindowVM;

                if (viewModel?.SelectedCompoundBean == null)
                {
                    // No data loaded - nothing to refresh
                    System.Diagnostics.Debug.WriteLine("No data loaded - skipping targeted theme refresh");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("TARGETED theme refresh: spectrum plots and structure image only");

                // TARGETED FIX 1: Refresh spectrum plot backgrounds ONLY if currently visible
                if (viewModel.SingleMassSpectrumUI != null && viewModel.TabMassSpectraView == TabMassSpectraView.SingleMS)
                {
                    // Quick background refresh for spectrum plot
                    viewModel.SingleMassSpectrumUI.RefreshUI();
                    System.Diagnostics.Debug.WriteLine("Refreshed single spectrum plot background");
                }

                if (viewModel.ConsensusSpectrumUI != null && viewModel.TabMassSpectraView == TabMassSpectraView.ConsensusMS)
                {
                    // Quick background refresh for consensus spectrum plot
                    viewModel.ConsensusSpectrumUI.RefreshUI();
                    System.Diagnostics.Debug.WriteLine("Refreshed consensus spectrum plot background");
                }

                // TARGETED FIX 2: Immediately refresh structure image for theme changes
                RefreshStructureImage(viewModel);
                System.Diagnostics.Debug.WriteLine("Refreshed structure image for theme change");

                themeStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"TARGETED theme refresh completed in {themeStopwatch.ElapsedMilliseconds}ms");

                if (themeStopwatch.ElapsedMilliseconds > 25)
                {
                    System.Diagnostics.Debug.WriteLine($"PERFORMANCE WARNING: Targeted refresh took {themeStopwatch.ElapsedMilliseconds}ms - target is <25ms");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in targeted theme refresh: {ex.Message}");
            }
        }

        /// <summary>
        /// LEGACY: Only refreshes currently visible mass spectrum components for fast theme switching
        /// </summary>
        private void RefreshVisibleMassSpectrumComponents()
        {
            try
            {
                var viewModel = this.DataContext as MainWindowVM;
                if (viewModel?.SelectedCompoundBean == null)
                {
                    // No data loaded - nothing to refresh
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"Refreshing visible components for theme change - TabView: {viewModel.TabMassSpectraView}");

                // OPTIMIZATION: Only refresh currently active spectrum view to avoid unnecessary work
                switch (viewModel.TabMassSpectraView)
                {
                    case TabMassSpectraView.SingleMS:
                        // Mark for refresh on next access instead of immediate refresh
                        viewModel.ShouldUpdateSingleMassSpectrumVM = true;
                        if (viewModel.SingleMassSpectrumUI != null)
                        {
                            viewModel.SingleMassSpectrumUI.RefreshUI();
                        }
                        break;

                    case TabMassSpectraView.ConsensusMS:
                        // Mark for refresh on next access instead of immediate refresh
                        viewModel.ShouldUpdateConsensusSpectrumVM = true;
                        if (viewModel.ConsensusSpectrumUI != null)
                        {
                            viewModel.ConsensusSpectrumUI.RefreshUI();
                        }
                        break;

                    case TabMassSpectraView.MultipleMS:
                        // Mark for refresh on next access instead of immediate refresh
                        viewModel.ShouldUpdateMultipleSpectrumVM = true;
                        break;
                }

                // OPTIMIZATION: Defer structure image update until next compound selection
                // This avoids expensive image regeneration during theme switching
                _structureImageNeedsRefresh = true;

                System.Diagnostics.Debug.WriteLine("Visible components refresh completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing visible mass spectrum components: {ex.Message}");
            }
        }

        /// <summary>
        /// LEGACY: Refreshes all mass spectrum UI components (kept for compatibility)
        /// </summary>
        private void RefreshMassSpectrumComponents()
        {
            try
            {
                var viewModel = this.DataContext as MainWindowVM;
                if (viewModel != null)
                {
                    // Force refresh of single mass spectrum UI
                    if (viewModel.SingleMassSpectrumUI != null)
                    {
                        viewModel.SingleMassSpectrumUI.RefreshUI();
                    }

                    // Force refresh of consensus spectrum UI
                    if (viewModel.ConsensusSpectrumUI != null)
                    {
                        viewModel.ConsensusSpectrumUI.RefreshUI();
                    }

                    // Force refresh of structure image to apply theme-aware background
                    RefreshStructureImage(viewModel);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing mass spectrum components: {ex.Message}");
            }
        }

        /// <summary>
        /// Refreshes the structure image to apply theme changes
        /// </summary>
        private void RefreshStructureImage(MainWindowVM viewModel)
        {
            try
            {
                // Check if deferred refresh is needed
                if (_structureImageNeedsRefresh)
                {
                    _structureImageNeedsRefresh = false;
                    System.Diagnostics.Debug.WriteLine("Applying deferred structure image refresh");
                }

                // Trigger structure image regeneration if there's a selected compound
                if (viewModel.SelectedCompoundBean != null && !string.IsNullOrEmpty(viewModel.SelectedCompoundBean.Smiles))
                {
                    // Force regeneration of the structure image with new theme colors
                    viewModel.UpdateStructureImage();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing structure image: {ex.Message}");
            }
        }

        /// <summary>
        /// Initializes the custom inertia timer for smooth touch momentum
        /// </summary>
        private void InitializeInertiaTimer()
        {
            _inertiaTimer = new System.Windows.Threading.DispatcherTimer();
            _inertiaTimer.Interval = TimeSpan.FromMilliseconds(16); // ~60 FPS for smooth animation
            _inertiaTimer.Tick += InertiaTimer_Tick;

            // Initialize manipulation tracking
            _isFirstManipulationDelta = true;
            _manipulationVelocity = new Vector(0, 0);
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Initialize the structure image zoom transform after the window is loaded
            if (StructureImage != null && StructureContainer != null)
            {
                // Get the ScaleTransform from XAML
                _structureImageTransform = StructureImageTransform;

                // Initialize zoom level display
                UpdateZoomLevelDisplay();

                // Center the image initially
                CenterStructureImage();
            }
        }

        /// <summary>
        /// Centers the structure image in the viewport and adjusts zoom if needed
        /// </summary>
        private void CenterStructureImage()
        {
            if (StructureScrollViewer == null || StructureImage == null || StructureImage.Source == null)
                return;

            // Wait for the image to be fully loaded and measured
            StructureImage.Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    // Get the actual size of the image
                    double imageWidth = StructureImage.ActualWidth;
                    double imageHeight = StructureImage.ActualHeight;

                    if (imageWidth <= 0 || imageHeight <= 0)
                    {
                        // If image dimensions aren't available yet, try again later
                        StructureImage.Dispatcher.BeginInvoke(new Action(CenterStructureImage),
                            System.Windows.Threading.DispatcherPriority.Background);
                        return;
                    }

                    // Only auto-adjust zoom on first load
                    if (_isFirstStructureLoad)
                    {
                        // Auto-fit the image to viewport on load
                        AutoFitImageToViewport();
                        _isFirstStructureLoad = false;
                    }

                    // Force layout update to ensure scrollable area is correct
                    StructureScrollViewer.UpdateLayout();

                    // Center the image in the viewport
                    CenterImageInViewport();
                }
                catch (Exception)
                {
                    // Silently handle any exceptions during centering
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        /// <summary>
        /// Centers the image in the current viewport
        /// </summary>
        private void CenterImageInViewport()
        {
            if (StructureScrollViewer == null || StructureImage == null)
                return;

            // Get viewport and content dimensions
            double viewportWidth = StructureScrollViewer.ViewportWidth;
            double viewportHeight = StructureScrollViewer.ViewportHeight;
            double contentWidth = StructureScrollViewer.ExtentWidth;
            double contentHeight = StructureScrollViewer.ExtentHeight;

            // Calculate center position
            double centerX = Math.Max(0, (contentWidth - viewportWidth) / 2);
            double centerY = Math.Max(0, (contentHeight - viewportHeight) / 2);

            // Scroll to center
            StructureScrollViewer.ScrollToHorizontalOffset(centerX);
            StructureScrollViewer.ScrollToVerticalOffset(centerY);
        }

        // Removed SynchronizeFilterWidths and FindVisualChild methods - no longer needed with fixed filter header

        /// <summary>
        /// ENHANCED: Event handler for when the DataGrid is loaded - initializes scroll synchronization
        /// </summary>
        private void DataGrid_CompoundTable_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Initialize scroll synchronization after DataGrid is fully loaded
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    InitializeScrollSynchronization();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in DataGrid_CompoundTable_Loaded: {ex.Message}");
            }
        }

        /// <summary>
        /// ENHANCED: Initialize scroll synchronization between filter row and DataGrid
        /// </summary>
        private void InitializeScrollSynchronization()
        {
            try
            {
                if (DataGrid_CompoundTable == null || FilterScrollViewer == null)
                {
                    System.Diagnostics.Debug.WriteLine("WARNING: DataGrid or FilterScrollViewer not available for scroll sync initialization");
                    return;
                }

                // Verify DataGrid ScrollViewer is accessible
                var dataGridScrollViewer = GetScrollViewer(DataGrid_CompoundTable);
                if (dataGridScrollViewer == null)
                {
                    System.Diagnostics.Debug.WriteLine("WARNING: Could not access DataGrid ScrollViewer for initialization");
                    return;
                }

                // Reset scroll positions to ensure perfect alignment
                FilterScrollViewer.ScrollToHorizontalOffset(0);
                dataGridScrollViewer.ScrollToHorizontalOffset(0);

                System.Diagnostics.Debug.WriteLine("Scroll synchronization initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing scroll synchronization: {ex.Message}");
            }
        }

        /// <summary>
        /// Event handler for manipulation boundary feedback to enable smooth horizontal touchpad scrolling
        /// </summary>
        private void DataGrid_ManipulationBoundaryFeedback(object sender, ManipulationBoundaryFeedbackEventArgs e)
        {
            // Allow smooth horizontal scrolling by not handling boundary feedback
            // This prevents the "bounce" effect and allows continuous scrolling
            e.Handled = true;
        }

        // Removed ScrollViewer_ScrollChanged method - no longer needed with fixed filter header

        /// <summary>
        /// Helper method to get the ScrollViewer from a DataGrid
        /// </summary>
        private ScrollViewer GetScrollViewer(DependencyObject depObj)
        {
            if (depObj == null) return null;

            // Check if the current object is a ScrollViewer
            if (depObj is ScrollViewer scrollViewer)
                return scrollViewer;

            // Search in the visual tree
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                var child = VisualTreeHelper.GetChild(depObj, i);
                var result = GetScrollViewer(child);
                if (result != null)
                    return result;
            }
            return null;
        }

        #region Interactive Zoom and Pan Functionality

        /// <summary>
        /// Updates the zoom level display text
        /// </summary>
        private void UpdateZoomLevelDisplay()
        {
            if (ZoomLevelText != null)
            {
                ZoomLevelText.Text = $"{Math.Round(_zoomFactor * 100)}%";
            }
        }

        /// <summary>
        /// Updates the Canvas size to match the scaled image size for proper scrolling
        /// </summary>
        private void UpdateCanvasSize()
        {
            if (StructureContainer != null && StructureImage != null && StructureImage.Source != null)
            {
                // Get the original image dimensions
                double imageWidth = StructureImage.Source.Width;
                double imageHeight = StructureImage.Source.Height;

                // Calculate the scaled dimensions
                double scaledWidth = imageWidth * _zoomFactor;
                double scaledHeight = imageHeight * _zoomFactor;

                // Update Canvas size to match scaled image
                StructureContainer.Width = scaledWidth;
                StructureContainer.Height = scaledHeight;

                // Force layout update to ensure scroll bars appear/update
                StructureScrollViewer.UpdateLayout();
            }
        }

        /// <summary>
        /// Event handler for the zoom in button
        /// </summary>
        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomAtCenter(_zoomFactor * _wheelZoomFactor);
        }

        /// <summary>
        /// Event handler for the zoom out button
        /// </summary>
        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomAtCenter(_zoomFactor / _wheelZoomFactor);
        }



        /// <summary>
        /// Event handler for the fit zoom button
        /// </summary>
        private void ZoomFitButton_Click(object sender, RoutedEventArgs e)
        {
            FitImageToViewport();
        }

        /// <summary>
        /// Event handler for when the structure image source is updated
        /// </summary>
        private void StructureImage_SourceUpdated(object sender, DataTransferEventArgs e)
        {
            // Reset the first load flag to trigger auto-fit calculation
            _isFirstStructureLoad = true;

            // Center the image with auto-fit
            Dispatcher.BeginInvoke(new Action(() => {
                CenterStructureImage();
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        /// <summary>
        /// Auto-fits the image to the viewport with padding (used on initial load)
        /// </summary>
        private void AutoFitImageToViewport()
        {
            if (StructureScrollViewer == null || StructureImage == null || StructureImage.Source == null)
                return;

            // Get image and viewport dimensions
            double imageWidth = StructureImage.Source.Width;
            double imageHeight = StructureImage.Source.Height;
            double viewportWidth = StructureScrollViewer.ViewportWidth;
            double viewportHeight = StructureScrollViewer.ViewportHeight;

            if (imageWidth <= 0 || imageHeight <= 0 || viewportWidth <= 0 || viewportHeight <= 0)
                return;

            // Calculate scale to fit with padding
            double scaleX = (viewportWidth * 0.9) / imageWidth;
            double scaleY = (viewportHeight * 0.9) / imageHeight;
            double fitZoom = Math.Min(scaleX, scaleY);

            // Apply fit zoom
            _zoomFactor = fitZoom;
            _structureImageTransform.ScaleX = _zoomFactor;
            _structureImageTransform.ScaleY = _zoomFactor;
            UpdateZoomLevelDisplay();
            UpdateCanvasSize();
        }

        /// <summary>
        /// Zooms to a specific level centered on the viewport
        /// </summary>
        private void ZoomAtCenter(double newZoom)
        {
            if (StructureScrollViewer == null || StructureImage == null)
                return;

            // Constrain zoom within limits
            newZoom = Math.Max(_minZoom, Math.Min(_maxZoom, newZoom));

            // Store current center point in content coordinates
            double viewportCenterX = StructureScrollViewer.HorizontalOffset + (StructureScrollViewer.ViewportWidth / 2);
            double viewportCenterY = StructureScrollViewer.VerticalOffset + (StructureScrollViewer.ViewportHeight / 2);

            // Calculate relative position (0-1) within the current content
            double relativeX = viewportCenterX / StructureScrollViewer.ExtentWidth;
            double relativeY = viewportCenterY / StructureScrollViewer.ExtentHeight;

            // Apply new zoom
            _zoomFactor = newZoom;
            _structureImageTransform.ScaleX = _zoomFactor;
            _structureImageTransform.ScaleY = _zoomFactor;
            UpdateZoomLevelDisplay();
            UpdateCanvasSize();

            // Force layout update
            StructureScrollViewer.UpdateLayout();

            // Calculate new center position and scroll to maintain relative position
            double newCenterX = relativeX * StructureScrollViewer.ExtentWidth;
            double newCenterY = relativeY * StructureScrollViewer.ExtentHeight;

            StructureScrollViewer.ScrollToHorizontalOffset(newCenterX - (StructureScrollViewer.ViewportWidth / 2));
            StructureScrollViewer.ScrollToVerticalOffset(newCenterY - (StructureScrollViewer.ViewportHeight / 2));
        }

        /// <summary>
        /// Zooms to a specific level at a specific point
        /// </summary>
        private void ZoomAtPoint(double newZoom, Point zoomPoint)
        {
            if (StructureScrollViewer == null || StructureImage == null)
                return;

            // Constrain zoom within limits
            newZoom = Math.Max(_minZoom, Math.Min(_maxZoom, newZoom));

            // Convert zoom point to content coordinates
            double contentX = StructureScrollViewer.HorizontalOffset + zoomPoint.X;
            double contentY = StructureScrollViewer.VerticalOffset + zoomPoint.Y;

            // Calculate relative position within current content
            double relativeX = contentX / StructureScrollViewer.ExtentWidth;
            double relativeY = contentY / StructureScrollViewer.ExtentHeight;

            // Apply new zoom
            _zoomFactor = newZoom;
            _structureImageTransform.ScaleX = _zoomFactor;
            _structureImageTransform.ScaleY = _zoomFactor;
            UpdateZoomLevelDisplay();
            UpdateCanvasSize();

            // Force layout update
            StructureScrollViewer.UpdateLayout();

            // Calculate new position and scroll to maintain zoom point
            double newContentX = relativeX * StructureScrollViewer.ExtentWidth;
            double newContentY = relativeY * StructureScrollViewer.ExtentHeight;

            StructureScrollViewer.ScrollToHorizontalOffset(newContentX - zoomPoint.X);
            StructureScrollViewer.ScrollToVerticalOffset(newContentY - zoomPoint.Y);
        }

        /// <summary>
        /// Fits the image to the viewport with padding
        /// </summary>
        private void FitImageToViewport()
        {
            if (StructureScrollViewer == null || StructureImage == null || StructureImage.Source == null)
                return;

            // Get image and viewport dimensions
            double imageWidth = StructureImage.Source.Width;
            double imageHeight = StructureImage.Source.Height;
            double viewportWidth = StructureScrollViewer.ViewportWidth;
            double viewportHeight = StructureScrollViewer.ViewportHeight;

            if (imageWidth <= 0 || imageHeight <= 0 || viewportWidth <= 0 || viewportHeight <= 0)
                return;

            // Calculate scale to fit with padding
            double scaleX = (viewportWidth * 0.9) / imageWidth;
            double scaleY = (viewportHeight * 0.9) / imageHeight;
            double fitZoom = Math.Min(scaleX, scaleY);

            // Apply fit zoom
            ZoomAtCenter(fitZoom);
        }

        /// <summary>
        /// Event handler for mouse wheel zoom with precise cursor-based zooming
        /// </summary>
        private void StructureScrollViewer_PreviewMouseWheel(object sender, MouseWheelEventArgs e)
        {
            if (Keyboard.Modifiers == ModifierKeys.Control)
            {
                // Get mouse position relative to the ScrollViewer
                Point mousePos = e.GetPosition(StructureScrollViewer);

                // Calculate zoom factor
                double zoomFactor = e.Delta > 0 ? _wheelZoomFactor : 1.0 / _wheelZoomFactor;
                double newZoom = _zoomFactor * zoomFactor;

                // Apply zoom at mouse cursor position
                ZoomAtPoint(newZoom, mousePos);

                e.Handled = true;
            }
        }

        /// <summary>
        /// Event handler for mouse down to start panning
        /// </summary>
        private void StructureScrollViewer_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (Keyboard.Modifiers == ModifierKeys.None && !_isManipulating)
            {
                // Stop any ongoing inertia when starting mouse pan
                _inertiaTimer.Stop();
                _manipulationVelocity = new Vector(0, 0);

                _isPanning = true;
                _panStartPoint = e.GetPosition(StructureScrollViewer);
                _panStartOffset = new Point(StructureScrollViewer.HorizontalOffset, StructureScrollViewer.VerticalOffset);
                StructureScrollViewer.CaptureMouse();
                StructureScrollViewer.Cursor = Cursors.Hand;
                e.Handled = true;
            }
        }

        /// <summary>
        /// Event handler for mouse move during panning
        /// </summary>
        private void StructureScrollViewer_PreviewMouseMove(object sender, MouseEventArgs e)
        {
            if (_isPanning && e.LeftButton == MouseButtonState.Pressed)
            {
                Point currentPoint = e.GetPosition(StructureScrollViewer);
                Vector delta = _panStartPoint - currentPoint;

                // Apply panning with smooth movement
                StructureScrollViewer.ScrollToHorizontalOffset(_panStartOffset.X + delta.X);
                StructureScrollViewer.ScrollToVerticalOffset(_panStartOffset.Y + delta.Y);

                e.Handled = true;
            }
        }

        /// <summary>
        /// Event handler for mouse up to end panning
        /// </summary>
        private void StructureScrollViewer_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isPanning)
            {
                _isPanning = false;
                StructureScrollViewer.ReleaseMouseCapture();
                StructureScrollViewer.Cursor = Cursors.Arrow;
                e.Handled = true;
            }
        }

        /// <summary>
        /// Custom inertia timer tick handler for smooth momentum
        /// </summary>
        private void InertiaTimer_Tick(object sender, EventArgs e)
        {
            // Apply velocity-based scrolling with deceleration
            const double deceleration = 0.95; // Deceleration factor (95% of previous velocity)
            const double minVelocity = 0.5; // Minimum velocity threshold to stop inertia

            // Apply current velocity to scroll position
            double newHorizontalOffset = StructureScrollViewer.HorizontalOffset + _manipulationVelocity.X;
            double newVerticalOffset = StructureScrollViewer.VerticalOffset + _manipulationVelocity.Y;

            // Constrain to valid scroll ranges
            newHorizontalOffset = Math.Max(0, Math.Min(StructureScrollViewer.ScrollableWidth, newHorizontalOffset));
            newVerticalOffset = Math.Max(0, Math.Min(StructureScrollViewer.ScrollableHeight, newVerticalOffset));

            StructureScrollViewer.ScrollToHorizontalOffset(newHorizontalOffset);
            StructureScrollViewer.ScrollToVerticalOffset(newVerticalOffset);

            // Apply deceleration
            _manipulationVelocity *= deceleration;

            // Stop inertia when velocity becomes too small
            if (_manipulationVelocity.Length < minVelocity)
            {
                _inertiaTimer.Stop();
                _manipulationVelocity = new Vector(0, 0);
            }
        }

        /// <summary>
        /// Event handler for manipulation starting (touch gestures)
        /// </summary>
        private void StructureScrollViewer_ManipulationStarting(object sender, ManipulationStartingEventArgs e)
        {
            _isManipulating = true;
            _initialZoom = _zoomFactor;
            _cumulativeZoomFactor = 1.0;

            // Stop any ongoing inertia
            _inertiaTimer.Stop();
            _manipulationVelocity = new Vector(0, 0);

            // Initialize velocity tracking - position will be set in first delta event
            _lastManipulationTime = DateTime.Now;
            _lastManipulationPosition = new Point(0, 0); // Will be updated in delta event
            _isFirstManipulationDelta = true;

            // Set the manipulation container to the ScrollViewer for proper coordinate handling
            e.ManipulationContainer = StructureScrollViewer;

            // Configure manipulation modes for better gesture recognition (.NET Framework 4.7.2 compatible)
            e.Mode = ManipulationModes.Scale | ManipulationModes.Translate;

            e.Handled = true;
        }

        /// <summary>
        /// Event handler for manipulation delta (touch zoom and pan)
        /// </summary>
        private void StructureScrollViewer_ManipulationDelta(object sender, ManipulationDeltaEventArgs e)
        {
            if (!_isManipulating) return;

            // Track velocity for inertia calculation
            DateTime currentTime = DateTime.Now;
            Point currentPosition = e.ManipulationOrigin;

            // Initialize position on first delta event
            if (_isFirstManipulationDelta)
            {
                _lastManipulationPosition = currentPosition;
                _lastManipulationTime = currentTime;
                _isFirstManipulationDelta = false;
            }
            else if ((currentTime - _lastManipulationTime).TotalMilliseconds > 0)
            {
                double deltaTime = (currentTime - _lastManipulationTime).TotalMilliseconds;
                Vector deltaPosition = currentPosition - _lastManipulationPosition;

                // Calculate velocity (pixels per millisecond, then convert to reasonable scale)
                // Only update velocity if there's significant movement to avoid noise
                if (deltaPosition.Length > 0.5)
                {
                    _manipulationVelocity = new Vector(
                        deltaPosition.X / deltaTime * 16, // Scale for 60fps animation
                        deltaPosition.Y / deltaTime * 16
                    );
                }

                _lastManipulationTime = currentTime;
                _lastManipulationPosition = currentPosition;
            }

            // Handle touch zoom (pinch gesture) with improved scaling
            if (Math.Abs(e.DeltaManipulation.Scale.X - 1.0) > 0.01 || Math.Abs(e.DeltaManipulation.Scale.Y - 1.0) > 0.01)
            {
                // Calculate average scale factor with smoothing
                double scaleFactor = (e.DeltaManipulation.Scale.X + e.DeltaManipulation.Scale.Y) / 2.0;

                // Apply cumulative scaling for smoother zoom
                _cumulativeZoomFactor *= scaleFactor;
                double newZoom = _initialZoom * _cumulativeZoomFactor;

                // Get the center point of the manipulation for zoom centering
                Point manipulationCenter = e.ManipulationOrigin;

                // Apply zoom at the manipulation center point with constraints
                ZoomAtPoint(newZoom, manipulationCenter);

                // Reset velocity during zoom to prevent unwanted panning
                _manipulationVelocity = new Vector(0, 0);
            }

            // Handle touch pan (single finger drag) - only if not scaling
            if ((Math.Abs(e.DeltaManipulation.Translation.X) > 1 || Math.Abs(e.DeltaManipulation.Translation.Y) > 1) &&
                Math.Abs(e.DeltaManipulation.Scale.X - 1.0) < 0.01 && Math.Abs(e.DeltaManipulation.Scale.Y - 1.0) < 0.01)
            {
                // Apply translation directly to scroll position with smooth movement
                double newHorizontalOffset = StructureScrollViewer.HorizontalOffset - e.DeltaManipulation.Translation.X;
                double newVerticalOffset = StructureScrollViewer.VerticalOffset - e.DeltaManipulation.Translation.Y;

                // Constrain to valid scroll ranges
                newHorizontalOffset = Math.Max(0, Math.Min(StructureScrollViewer.ScrollableWidth, newHorizontalOffset));
                newVerticalOffset = Math.Max(0, Math.Min(StructureScrollViewer.ScrollableHeight, newVerticalOffset));

                StructureScrollViewer.ScrollToHorizontalOffset(newHorizontalOffset);
                StructureScrollViewer.ScrollToVerticalOffset(newVerticalOffset);
            }

            e.Handled = true;
        }

        /// <summary>
        /// Event handler for manipulation completed (touch gestures end)
        /// </summary>
        private void StructureScrollViewer_ManipulationCompleted(object sender, ManipulationCompletedEventArgs e)
        {
            _isManipulating = false;
            _isFirstManipulationDelta = true; // Reset for next manipulation

            // Start custom inertia if there's sufficient velocity
            const double minInertiaVelocity = 2.0; // Minimum velocity to trigger inertia

            if (_manipulationVelocity.Length > minInertiaVelocity)
            {
                // Limit maximum inertia velocity for better control
                const double maxInertiaVelocity = 20.0;
                if (_manipulationVelocity.Length > maxInertiaVelocity)
                {
                    _manipulationVelocity = _manipulationVelocity / _manipulationVelocity.Length * maxInertiaVelocity;
                }

                // Start the inertia timer for smooth deceleration
                _inertiaTimer.Start();
            }
            else
            {
                // No significant velocity, stop immediately
                _manipulationVelocity = new Vector(0, 0);
            }

            e.Handled = true;
        }

        /// <summary>
        /// Event handler for manipulation boundary feedback (touch gestures)
        /// </summary>
        private void StructureScrollViewer_ManipulationBoundaryFeedback(object sender, ManipulationBoundaryFeedbackEventArgs e)
        {
            // Prevent bouncing at boundaries for smoother experience
            e.Handled = true;
        }

        /// <summary>
        /// Event handler for keyboard navigation
        /// </summary>
        private void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            // Only handle keys when the structure area has focus or no specific control has focus
            if (StructureScrollViewer == null || !StructureScrollViewer.IsVisible)
                return;

            bool handled = false;
            const double panStep = 50; // Pixels to pan per key press

            switch (e.Key)
            {
                case Key.Add:
                case Key.OemPlus:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        ZoomAtCenter(_zoomFactor * _wheelZoomFactor);
                        handled = true;
                    }
                    break;

                case Key.Subtract:
                case Key.OemMinus:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        ZoomAtCenter(_zoomFactor / _wheelZoomFactor);
                        handled = true;
                    }
                    break;

                case Key.D0:
                case Key.NumPad0:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        ZoomAtCenter(1.0); // Reset to 100%
                        handled = true;
                    }
                    break;

                case Key.F:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        FitImageToViewport(); // Fit to window
                        handled = true;
                    }
                    break;

                case Key.Left:
                    StructureScrollViewer.ScrollToHorizontalOffset(StructureScrollViewer.HorizontalOffset - panStep);
                    handled = true;
                    break;

                case Key.Right:
                    StructureScrollViewer.ScrollToHorizontalOffset(StructureScrollViewer.HorizontalOffset + panStep);
                    handled = true;
                    break;

                case Key.Up:
                    StructureScrollViewer.ScrollToVerticalOffset(StructureScrollViewer.VerticalOffset - panStep);
                    handled = true;
                    break;

                case Key.Down:
                    StructureScrollViewer.ScrollToVerticalOffset(StructureScrollViewer.VerticalOffset + panStep);
                    handled = true;
                    break;

                case Key.Home:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        // Go to top-left corner
                        StructureScrollViewer.ScrollToHorizontalOffset(0);
                        StructureScrollViewer.ScrollToVerticalOffset(0);
                        handled = true;
                    }
                    break;

                case Key.End:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        // Go to bottom-right corner
                        StructureScrollViewer.ScrollToHorizontalOffset(StructureScrollViewer.ExtentWidth);
                        StructureScrollViewer.ScrollToVerticalOffset(StructureScrollViewer.ExtentHeight);
                        handled = true;
                    }
                    break;

                case Key.Space:
                    // Center the image
                    CenterImageInViewport();
                    handled = true;
                    break;
            }

            if (handled)
            {
                e.Handled = true;
            }
        }

        #endregion

        /// <summary>
        /// Event handler for when columns are reordered
        /// </summary>
        private void DataGrid_CompoundTable_ColumnReordered(object sender, EventArgs e)
        {
            // No longer need synchronization with fixed filter header
        }

        /// <summary>
        /// Event handler for when column display indices change
        /// </summary>
        private void DataGrid_CompoundTable_ColumnDisplayIndexChanged(object sender, DataGridColumnEventArgs e)
        {
            // No longer need synchronization with fixed filter header
        }

        /// <summary>
        /// ENHANCED: Robust helper method to get the ScrollViewer from a DataGrid with comprehensive visual tree traversal
        /// </summary>
        private ScrollViewer GetScrollViewer(DataGrid dataGrid)
        {
            if (dataGrid == null) return null;

            try
            {
                // Method 1: Try direct template access (fastest)
                if (dataGrid.Template != null)
                {
                    var scrollViewer = dataGrid.Template.FindName("DG_ScrollViewer", dataGrid) as ScrollViewer;
                    if (scrollViewer != null) return scrollViewer;
                }

                // Method 2: Comprehensive visual tree traversal
                return FindVisualChild<ScrollViewer>(dataGrid);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting ScrollViewer: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// ENHANCED: Comprehensive visual tree traversal to find child of specific type
        /// </summary>
        private T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            // Check if parent is the type we're looking for
            if (parent is T result) return result;

            // Recursively search children
            int childCount = VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                var foundChild = FindVisualChild<T>(child);
                if (foundChild != null) return foundChild;
            }

            return null;
        }

        /// <summary>
        /// ENHANCED BIDIRECTIONAL SCROLL SYNC: Filter ScrollViewer scroll changes - synchronizes DataGrid with filter horizontal scrolling
        /// </summary>
        private void FilterScrollViewer_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // Prevent infinite recursion during synchronization
            if (_isScrollSyncInProgress) return;

            // Only sync horizontal scroll changes with comprehensive validation
            if (e.HorizontalChange != 0 && sender is ScrollViewer filterScrollViewer && DataGrid_CompoundTable != null)
            {
                _isScrollSyncInProgress = true;
                try
                {
                    // Get the DataGrid's ScrollViewer with enhanced error handling
                    var dataGridScrollViewer = GetScrollViewer(DataGrid_CompoundTable);
                    if (dataGridScrollViewer != null)
                    {
                        // ENHANCED: Validate scroll boundaries and prevent drift
                        double targetOffset = filterScrollViewer.HorizontalOffset;
                        double maxOffset = Math.Max(0, dataGridScrollViewer.ScrollableWidth);

                        // Clamp offset to valid range
                        targetOffset = Math.Max(0, Math.Min(maxOffset, targetOffset));

                        // Synchronize with enhanced precision tolerance and boundary validation
                        double currentOffset = dataGridScrollViewer.HorizontalOffset;
                        if (Math.Abs(currentOffset - targetOffset) > 0.1) // Reduced tolerance for better precision
                        {
                            dataGridScrollViewer.ScrollToHorizontalOffset(targetOffset);
                            System.Diagnostics.Debug.WriteLine($"Filter->DataGrid sync: {targetOffset:F2} (max: {maxOffset:F2})");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("WARNING: DataGrid ScrollViewer not available for Filter->DataGrid sync");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"ERROR in FilterScrollViewer_ScrollChanged: {ex.Message}");
                }
                finally
                {
                    _isScrollSyncInProgress = false;
                }
            }
        }

        /// <summary>
        /// ENHANCED BIDIRECTIONAL SCROLL SYNC: DataGrid scroll changes - synchronizes filter row with DataGrid horizontal scrolling
        /// </summary>
        private void DataGrid_CompoundTable_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // Prevent infinite recursion during synchronization
            if (_isScrollSyncInProgress) return;

            // Only sync horizontal scroll changes with comprehensive validation
            if (e.HorizontalChange != 0 && FilterScrollViewer != null)
            {
                _isScrollSyncInProgress = true;
                try
                {
                    // ENHANCED: Get the DataGrid's ScrollViewer with multiple fallback methods
                    ScrollViewer dataGridScrollViewer = null;

                    // Method 1: Sender is the ScrollViewer (most common case)
                    dataGridScrollViewer = sender as ScrollViewer;

                    // Method 2: Fallback - get from DataGrid if sender is not ScrollViewer
                    if (dataGridScrollViewer == null && DataGrid_CompoundTable != null)
                    {
                        dataGridScrollViewer = GetScrollViewer(DataGrid_CompoundTable);
                    }

                    if (dataGridScrollViewer != null)
                    {
                        // ENHANCED: Validate scroll boundaries and prevent drift
                        double targetOffset = dataGridScrollViewer.HorizontalOffset;
                        double maxOffset = Math.Max(0, FilterScrollViewer.ScrollableWidth);

                        // Clamp offset to valid range
                        targetOffset = Math.Max(0, Math.Min(maxOffset, targetOffset));

                        // Synchronize with enhanced precision tolerance and boundary validation
                        double currentOffset = FilterScrollViewer.HorizontalOffset;
                        if (Math.Abs(currentOffset - targetOffset) > 0.1) // Reduced tolerance for better precision
                        {
                            FilterScrollViewer.ScrollToHorizontalOffset(targetOffset);
                            System.Diagnostics.Debug.WriteLine($"DataGrid->Filter sync: {targetOffset:F2} (max: {maxOffset:F2})");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("WARNING: Could not access DataGrid ScrollViewer for DataGrid->Filter sync");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"ERROR in DataGrid_CompoundTable_ScrollChanged: {ex.Message}");
                }
                finally
                {
                    _isScrollSyncInProgress = false;
                }
            }
        }

        /// <summary>
        /// ENHANCED: Handle window size changes to maintain scroll synchronization
        /// </summary>
        private void MainWindow_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            try
            {
                // Delay scroll synchronization reset to allow layout to complete
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ResetScrollSynchronization();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in MainWindow_SizeChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// ENHANCED: Handle theme changes to maintain scroll synchronization
        /// </summary>
        private void ThemeService_ThemeChanged(object sender, EventArgs e)
        {
            try
            {
                // Delay scroll synchronization reset to allow theme change to complete
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ResetScrollSynchronization();
                }), System.Windows.Threading.DispatcherPriority.ApplicationIdle);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ThemeService_ThemeChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// ENHANCED: Reset scroll synchronization to ensure perfect alignment
        /// </summary>
        private void ResetScrollSynchronization()
        {
            try
            {
                if (DataGrid_CompoundTable == null || FilterScrollViewer == null)
                    return;

                var dataGridScrollViewer = GetScrollViewer(DataGrid_CompoundTable);
                if (dataGridScrollViewer == null)
                    return;

                // Reset both scroll positions to 0 to ensure perfect alignment
                _isScrollSyncInProgress = true;
                try
                {
                    FilterScrollViewer.ScrollToHorizontalOffset(0);
                    dataGridScrollViewer.ScrollToHorizontalOffset(0);
                    System.Diagnostics.Debug.WriteLine("Scroll synchronization reset completed");
                }
                finally
                {
                    _isScrollSyncInProgress = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error resetting scroll synchronization: {ex.Message}");
            }
        }

        // Flag to prevent infinite recursion during scroll synchronization
        private bool _isScrollSyncInProgress = false;

        /// <summary>
        /// Updates the DataGrid columns based on the available column information from the ViewModel
        /// </summary>
        public void UpdateDynamicColumns()
        {
            try
            {
                var viewModel = this.DataContext as MainWindowVM;
                if (viewModel?.AvailableColumns == null || DataGrid_CompoundTable == null)
                {
                    System.Diagnostics.Debug.WriteLine("UpdateDynamicColumns: ViewModel or DataGrid not available");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"UpdateDynamicColumns: Updating columns based on {viewModel.AvailableColumns.Count} available columns");

                // Use the DynamicColumnManager to update the DataGrid
                DynamicColumnManager.UpdateDataGridColumns(DataGrid_CompoundTable, viewModel.AvailableColumns, FilterGrid);

                // Reset scroll synchronization after column changes
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    ResetScrollSynchronization();
                }), System.Windows.Threading.DispatcherPriority.Loaded);

                System.Diagnostics.Debug.WriteLine("UpdateDynamicColumns: Column update completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in UpdateDynamicColumns: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        #region Custom Window Controls

        /// <summary>
        /// Handle title bar mouse down for window dragging
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // Double-click to maximize/restore
                MaximizeButton_Click(sender, e);
            }
            else
            {
                // Single click to drag
                this.DragMove();
            }
        }

        /// <summary>
        /// Minimize window
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// Maximize or restore window - TEMPORARILY SIMPLIFIED FOR TESTING
        /// </summary>
        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (this.WindowState == WindowState.Maximized)
            {
                this.WindowState = WindowState.Normal;
                // Update maximize icon to show maximize symbol
                // if (MaximizeIcon != null)
                // {
                //     MaximizeIcon.Data = Geometry.Parse("M0,0 L10,0 L10,10 L0,10 Z");
                // }
            }
            else
            {
                this.WindowState = WindowState.Maximized;
                // Update maximize icon to show restore symbol
                // if (MaximizeIcon != null)
                // {
                //     MaximizeIcon.Data = Geometry.Parse("M0,2 L8,2 L8,10 L0,10 Z M2,0 L10,0 L10,8 L8,8");
                // }
            }
        }

        /// <summary>
        /// Close window
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Auto-Import Database

        /// <summary>
        /// Auto-import database on startup
        /// </summary>
        private async void MainWindow_AutoImportDatabase(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = this.DataContext as MainWindowVM;
                if (viewModel == null) return;

                // Path to the database file
                string databasePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "NQI_MSMS_database_20250523.msp");

                if (System.IO.File.Exists(databasePath))
                {
                    System.Diagnostics.Debug.WriteLine($"Auto-importing database from: {databasePath}");

                    // Show loading indicator
                    viewModel.IsImportInProgress = true;
                    viewModel.IsImportIndeterminate = true;

                    // Import the database asynchronously
                    await Task.Run(() =>
                    {
                        try
                        {
                            // Use the correct method to set library directly
                            viewModel.MsLimaData.DataStorage.SetLibrary(databasePath, viewModel.MsLimaData.Parameter.CompoundGroupingKey);

                            // Update UI on main thread
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                viewModel.ImportFile();
                                System.Diagnostics.Debug.WriteLine("Auto-import completed successfully");
                            });
                        }
                        catch (Exception importEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error during auto-import: {importEx.Message}");

                            // Show error message on main thread
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                MessageBox.Show($"Failed to auto-import database: {importEx.Message}",
                                              "Auto-Import Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                            });
                        }
                    });
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Database file not found: {databasePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in auto-import: {ex.Message}");
            }
            finally
            {
                // Hide loading indicator
                var viewModel = this.DataContext as MainWindowVM;
                if (viewModel != null)
                {
                    viewModel.IsImportInProgress = false;
                    viewModel.IsImportIndeterminate = false;
                }
            }
        }

        #endregion
    }
}
