﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows.Threading;
using System.Threading;
using System.Collections.Concurrent;
using System.Windows;
using System.Diagnostics;
using Metabolomics.Core;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima
{
    public class FilteredTable : ViewModelBase
    {
        private ICollectionView view;
        public ICollectionView View {
            get => view;
            private set => OnPropertyChangedIfSet(ref view, value, nameof(View)); }

        public FilteredTable(System.Collections.IList list)
        {
            View = System.Windows.Data.CollectionViewSource.GetDefaultView(list);
        }
    }

    public class FilterSettingsForLibrary : ViewModelBase
    {
        #region member and variables

        private string _idFilter = "";
        private string _metaboliteNameFilter = "";
        private string _inchiKeyFilter = "";
        private string _mzFilter = "";
        private string _rtFilter = "";

        private ICollectionView view;

        // High-performance filtering with debouncing
        private CancellationTokenSource _filterCancellationTokenSource;
        private readonly int _filterDelayMs = 300; // Debounce delay - wait for user to stop typing

        public string IdFilter {
            get { return _idFilter; }
            set { if (_idFilter == value) return; _idFilter = value; Update(); OnPropertyChanged("IdFilter"); }
        }

        public string MetaboliteNameFilter {
            get { return _metaboliteNameFilter; }
            set { if (_metaboliteNameFilter == value) return; _metaboliteNameFilter = value; Update(); OnPropertyChanged("MetaboliteNameFilter"); }
        }

        public string InChIKeyFilter {
            get { return _inchiKeyFilter; }
            set { if (_inchiKeyFilter == value) return; _inchiKeyFilter = value; Update(); OnPropertyChanged("InChIKeyFilter"); }
        }

        public string RetentionTimeFilter {
            get { return _rtFilter; }
            set { if (_rtFilter == value) return; _rtFilter = value; Update(); OnPropertyChanged("RetentionTimeFilter"); }
        }

        public string MzFilter {
            get { return _mzFilter; }
            set { if (_mzFilter == value) return; _mzFilter = value; Update(); OnPropertyChanged("MzFilter"); }
        }

        /// <summary>
        /// ULTRA-HIGH-PERFORMANCE filtering update - uses immediate filter assignment instead of view.Refresh()
        /// This approach avoids the expensive view.Refresh() operation that causes UI blocking
        /// </summary>
        private void Update()
        {
            try
            {
                // SAFETY CHECK: Ensure view is not null before attempting filter operations
                if (this.view == null)
                {
                    System.Diagnostics.Debug.WriteLine("WARNING: view is null in Update() - skipping filter update");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("ULTRA-FAST filter update - immediate filter assignment");

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                // REVOLUTIONARY APPROACH: Instead of calling view.Refresh(), just reassign the filter
                // This triggers WPF's built-in change notification without the expensive refresh operation
                var currentFilter = this.view.Filter;

                // SAFETY CHECK: Only reassign if we have a valid filter function
                if (currentFilter != null)
                {
                    this.view.Filter = null;  // Clear filter
                    this.view.Filter = currentFilter;  // Reassign filter - triggers automatic refresh
                }
                else
                {
                    // If no filter is set, just refresh the view
                    this.view.Refresh();
                }

                stopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"ULTRA-FAST filter applied in {stopwatch.ElapsedMilliseconds}ms (no view.Refresh() needed)");

                if (stopwatch.ElapsedMilliseconds > 50)
                {
                    System.Diagnostics.Debug.WriteLine($"PERFORMANCE WARNING: Filter update took {stopwatch.ElapsedMilliseconds}ms - target is <50ms");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ultra-fast filter update: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // ROBUST FALLBACK: Try traditional refresh if filter reassignment fails
                try
                {
                    if (this.view != null)
                    {
                        this.view.Refresh();
                        System.Diagnostics.Debug.WriteLine("Fallback to view.Refresh() succeeded");
                    }
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"CRITICAL: Fallback refresh also failed: {fallbackEx.Message}");
                    System.Diagnostics.Debug.WriteLine($"Fallback stack trace: {fallbackEx.StackTrace}");
                }
            }
        }

        /// <summary>
        /// ULTRA-ROBUST clear all filters - prevents all crash scenarios
        /// </summary>
        public void ClearAllFilters()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("FilteredTableCollection.ClearAllFilters: Starting ULTRA-ROBUST filter clear operation");

                // SAFETY LEVEL 1: Clear original filters with individual error handling
                try { IdFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing IdFilter: {ex.Message}"); }
                try { MetaboliteNameFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing MetaboliteNameFilter: {ex.Message}"); }
                try { InChIKeyFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing InChIKeyFilter: {ex.Message}"); }
                try { RetentionTimeFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing RetentionTimeFilter: {ex.Message}"); }
                try { MzFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing MzFilter: {ex.Message}"); }
                try { CasFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing CasFilter: {ex.Message}"); }

                // SAFETY LEVEL 2: Clear comprehensive filters with individual error handling
                try { CidFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing CidFilter: {ex.Message}"); }
                try { PrecursorTypeFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing PrecursorTypeFilter: {ex.Message}"); }
                try { CollisionEnergyFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing CollisionEnergyFilter: {ex.Message}"); }
                try { CramerRulesFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing CramerRulesFilter: {ex.Message}"); }
                try { SvhcFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing SvhcFilter: {ex.Message}"); }
                try { CmrFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing CmrFilter: {ex.Message}"); }
                try { CmrSuspectFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing CmrSuspectFilter: {ex.Message}"); }
                try { EdcFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing EdcFilter: {ex.Message}"); }
                try { IarcFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing IarcFilter: {ex.Message}"); }
                try { EuSmlFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing EuSmlFilter: {ex.Message}"); }
                try { ChinaSmlFilter = ""; } catch (Exception ex) { System.Diagnostics.Debug.WriteLine($"Error clearing ChinaSmlFilter: {ex.Message}"); }

                System.Diagnostics.Debug.WriteLine("FilteredTableCollection.ClearAllFilters: All comprehensive filters cleared successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR in FilteredTableCollection.ClearAllFilters: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // ULTIMATE FALLBACK: Clear filters directly via private fields
                try
                {
                    System.Diagnostics.Debug.WriteLine("FilteredTableCollection.ClearAllFilters: Attempting ULTIMATE FALLBACK via private fields");

                    // Clear original filters directly
                    _idFilter = "";
                    _metaboliteNameFilter = "";
                    _inchiKeyFilter = "";
                    _mzFilter = "";
                    _rtFilter = "";
                    _casFilter = "";

                    // Clear new filters directly
                    _cidFilter = "";
                    _precursorTypeFilter = "";
                    _collisionEnergyFilter = "";
                    _cramerRulesFilter = "";
                    _svhcFilter = "";
                    _cmrFilter = "";
                    _cmrSuspectFilter = "";
                    _edcFilter = "";
                    _iarcFilter = "";
                    _euSmlFilter = "";
                    _chinaSmlFilter = "";

                    // SAFE view refresh with null checking
                    if (this.view != null)
                    {
                        try
                        {
                            // Use ultra-fast filter reassignment instead of expensive Refresh()
                            var currentFilter = this.view.Filter;
                            if (currentFilter != null)
                            {
                                this.view.Filter = null;
                                this.view.Filter = currentFilter;
                            }
                            else
                            {
                                this.view.Refresh();
                            }
                        }
                        catch (Exception refreshEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error during view refresh: {refreshEx.Message}");
                            // Continue - view refresh is not critical
                        }
                    }

                    System.Diagnostics.Debug.WriteLine("FilteredTableCollection.ClearAllFilters: ULTIMATE FALLBACK succeeded");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"CRITICAL: FilteredTableCollection.ClearAllFilters ULTIMATE FALLBACK failed: {fallbackEx.Message}");
                    // At this point, we've done everything possible - just continue without crashing
                }
            }
        }



        // COMPREHENSIVE filter support for all major fields
        private string _cidFilter = "";
        private string _precursorTypeFilter = "";
        private string _collisionEnergyFilter = "";
        private string _cramerRulesFilter = "";
        private string _svhcFilter = "";
        private string _cmrFilter = "";
        private string _cmrSuspectFilter = "";
        private string _edcFilter = "";
        private string _iarcFilter = "";
        private string _euSmlFilter = "";
        private string _chinaSmlFilter = "";

        // Additional filter properties with ultra-fast filtering
        public string CidFilter {
            get { return _cidFilter; }
            set { if (_cidFilter == value) return; _cidFilter = value; Update(); OnPropertyChanged("CidFilter"); }
        }

        // NEW FILTER PROPERTIES FOR MISSING COLUMNS
        private string _precursorMzFilter = "";
        private string _ionModeFilter = "";
        private string _formulaFilter = "";
        private string _exactMassFilter = "";
        private string _carcinogenicityIssFilter = "";
        private string _dnaAlertsOasisFilter = "";
        private string _dnaBindingOasisFilter = "";
        private string _dnaBindingOecdFilter = "";
        private string _proteinBindingAlertsOasisFilter = "";
        private string _vitroMutagenicityAlertsIssFilter = "";
        private string _vivoMutagenicityAlertsIssFilter = "";
        private string _smilesFilter = "";
        private string _inchiFilter = "";
        private string _inchikeyFilter = "";
        private string _iupacNameFilter = "";
        private string _superclassFilter = "";
        private string _classFilter = "";
        private string _subclassFilter = "";
        private string _ontologyFilter = "";
        private string _xlogpFilter = "";
        private string _mlogpFilter = "";
        private string _alogpFilter = "";
        private string _topoPsaFilter = "";
        private string _numBondFilter = "";
        private string _numAtomFilter = "";
        private string _authorsFilter = "";
        private string _instrumentFilter = "";
        private string _instrumentTypeFilter = "";
        private string _dateFilter = "";
        private string _numPeaksFilter = "";

        public string PrecursorMzFilter {
            get { return _precursorMzFilter; }
            set { if (_precursorMzFilter == value) return; _precursorMzFilter = value; Update(); OnPropertyChanged("PrecursorMzFilter"); }
        }

        public string IonModeFilter {
            get { return _ionModeFilter; }
            set { if (_ionModeFilter == value) return; _ionModeFilter = value; Update(); OnPropertyChanged("IonModeFilter"); }
        }

        public string FormulaFilter {
            get { return _formulaFilter; }
            set { if (_formulaFilter == value) return; _formulaFilter = value; Update(); OnPropertyChanged("FormulaFilter"); }
        }

        public string ExactMassFilter {
            get { return _exactMassFilter; }
            set { if (_exactMassFilter == value) return; _exactMassFilter = value; Update(); OnPropertyChanged("ExactMassFilter"); }
        }

        public string CarcinogenicityIssFilter {
            get { return _carcinogenicityIssFilter; }
            set { if (_carcinogenicityIssFilter == value) return; _carcinogenicityIssFilter = value; Update(); OnPropertyChanged("CarcinogenicityIssFilter"); }
        }

        public string DnaAlertsOasisFilter {
            get { return _dnaAlertsOasisFilter; }
            set { if (_dnaAlertsOasisFilter == value) return; _dnaAlertsOasisFilter = value; Update(); OnPropertyChanged("DnaAlertsOasisFilter"); }
        }

        public string DnaBindingOasisFilter {
            get { return _dnaBindingOasisFilter; }
            set { if (_dnaBindingOasisFilter == value) return; _dnaBindingOasisFilter = value; Update(); OnPropertyChanged("DnaBindingOasisFilter"); }
        }

        public string DnaBindingOecdFilter {
            get { return _dnaBindingOecdFilter; }
            set { if (_dnaBindingOecdFilter == value) return; _dnaBindingOecdFilter = value; Update(); OnPropertyChanged("DnaBindingOecdFilter"); }
        }

        public string ProteinBindingAlertsOasisFilter {
            get { return _proteinBindingAlertsOasisFilter; }
            set { if (_proteinBindingAlertsOasisFilter == value) return; _proteinBindingAlertsOasisFilter = value; Update(); OnPropertyChanged("ProteinBindingAlertsOasisFilter"); }
        }

        public string VitroMutagenicityAlertsIssFilter {
            get { return _vitroMutagenicityAlertsIssFilter; }
            set { if (_vitroMutagenicityAlertsIssFilter == value) return; _vitroMutagenicityAlertsIssFilter = value; Update(); OnPropertyChanged("VitroMutagenicityAlertsIssFilter"); }
        }

        public string VivoMutagenicityAlertsIssFilter {
            get { return _vivoMutagenicityAlertsIssFilter; }
            set { if (_vivoMutagenicityAlertsIssFilter == value) return; _vivoMutagenicityAlertsIssFilter = value; Update(); OnPropertyChanged("VivoMutagenicityAlertsIssFilter"); }
        }

        public string SmilesFilter {
            get { return _smilesFilter; }
            set { if (_smilesFilter == value) return; _smilesFilter = value; Update(); OnPropertyChanged("SmilesFilter"); }
        }

        public string InchiFilter {
            get { return _inchiFilter; }
            set { if (_inchiFilter == value) return; _inchiFilter = value; Update(); OnPropertyChanged("InchiFilter"); }
        }

        public string InchikeyFilter {
            get { return _inchikeyFilter; }
            set { if (_inchikeyFilter == value) return; _inchikeyFilter = value; Update(); OnPropertyChanged("InchikeyFilter"); }
        }

        public string IupacNameFilter {
            get { return _iupacNameFilter; }
            set { if (_iupacNameFilter == value) return; _iupacNameFilter = value; Update(); OnPropertyChanged("IupacNameFilter"); }
        }

        public string SuperclassFilter {
            get { return _superclassFilter; }
            set { if (_superclassFilter == value) return; _superclassFilter = value; Update(); OnPropertyChanged("SuperclassFilter"); }
        }

        public string ClassFilter {
            get { return _classFilter; }
            set { if (_classFilter == value) return; _classFilter = value; Update(); OnPropertyChanged("ClassFilter"); }
        }

        public string SubclassFilter {
            get { return _subclassFilter; }
            set { if (_subclassFilter == value) return; _subclassFilter = value; Update(); OnPropertyChanged("SubclassFilter"); }
        }

        public string OntologyFilter {
            get { return _ontologyFilter; }
            set { if (_ontologyFilter == value) return; _ontologyFilter = value; Update(); OnPropertyChanged("OntologyFilter"); }
        }

        public string XlogpFilter {
            get { return _xlogpFilter; }
            set { if (_xlogpFilter == value) return; _xlogpFilter = value; Update(); OnPropertyChanged("XlogpFilter"); }
        }

        public string MlogpFilter {
            get { return _mlogpFilter; }
            set { if (_mlogpFilter == value) return; _mlogpFilter = value; Update(); OnPropertyChanged("MlogpFilter"); }
        }

        public string AlogpFilter {
            get { return _alogpFilter; }
            set { if (_alogpFilter == value) return; _alogpFilter = value; Update(); OnPropertyChanged("AlogpFilter"); }
        }

        public string TopoPsaFilter {
            get { return _topoPsaFilter; }
            set { if (_topoPsaFilter == value) return; _topoPsaFilter = value; Update(); OnPropertyChanged("TopoPsaFilter"); }
        }

        public string NumBondFilter {
            get { return _numBondFilter; }
            set { if (_numBondFilter == value) return; _numBondFilter = value; Update(); OnPropertyChanged("NumBondFilter"); }
        }

        public string NumAtomFilter {
            get { return _numAtomFilter; }
            set { if (_numAtomFilter == value) return; _numAtomFilter = value; Update(); OnPropertyChanged("NumAtomFilter"); }
        }

        public string AuthorsFilter {
            get { return _authorsFilter; }
            set { if (_authorsFilter == value) return; _authorsFilter = value; Update(); OnPropertyChanged("AuthorsFilter"); }
        }

        public string InstrumentFilter {
            get { return _instrumentFilter; }
            set { if (_instrumentFilter == value) return; _instrumentFilter = value; Update(); OnPropertyChanged("InstrumentFilter"); }
        }

        public string InstrumentTypeFilter {
            get { return _instrumentTypeFilter; }
            set { if (_instrumentTypeFilter == value) return; _instrumentTypeFilter = value; Update(); OnPropertyChanged("InstrumentTypeFilter"); }
        }

        public string DateFilter {
            get { return _dateFilter; }
            set { if (_dateFilter == value) return; _dateFilter = value; Update(); OnPropertyChanged("DateFilter"); }
        }

        public string NumPeaksFilter {
            get { return _numPeaksFilter; }
            set { if (_numPeaksFilter == value) return; _numPeaksFilter = value; Update(); OnPropertyChanged("NumPeaksFilter"); }
        }

        public string PrecursorTypeFilter {
            get { return _precursorTypeFilter; }
            set { if (_precursorTypeFilter == value) return; _precursorTypeFilter = value; Update(); OnPropertyChanged("PrecursorTypeFilter"); }
        }

        public string CollisionEnergyFilter {
            get { return _collisionEnergyFilter; }
            set { if (_collisionEnergyFilter == value) return; _collisionEnergyFilter = value; Update(); OnPropertyChanged("CollisionEnergyFilter"); }
        }

        public string CramerRulesFilter {
            get { return _cramerRulesFilter; }
            set { if (_cramerRulesFilter == value) return; _cramerRulesFilter = value; Update(); OnPropertyChanged("CramerRulesFilter"); }
        }

        public string SvhcFilter {
            get { return _svhcFilter; }
            set { if (_svhcFilter == value) return; _svhcFilter = value; Update(); OnPropertyChanged("SvhcFilter"); }
        }

        public string CmrFilter {
            get { return _cmrFilter; }
            set { if (_cmrFilter == value) return; _cmrFilter = value; Update(); OnPropertyChanged("CmrFilter"); }
        }

        public string CmrSuspectFilter {
            get { return _cmrSuspectFilter; }
            set { if (_cmrSuspectFilter == value) return; _cmrSuspectFilter = value; Update(); OnPropertyChanged("CmrSuspectFilter"); }
        }

        public string EdcFilter {
            get { return _edcFilter; }
            set { if (_edcFilter == value) return; _edcFilter = value; Update(); OnPropertyChanged("EdcFilter"); }
        }

        public string IarcFilter {
            get { return _iarcFilter; }
            set { if (_iarcFilter == value) return; _iarcFilter = value; Update(); OnPropertyChanged("IarcFilter"); }
        }

        public string EuSmlFilter {
            get { return _euSmlFilter; }
            set { if (_euSmlFilter == value) return; _euSmlFilter = value; Update(); OnPropertyChanged("EuSmlFilter"); }
        }

        public string ChinaSmlFilter {
            get { return _chinaSmlFilter; }
            set { if (_chinaSmlFilter == value) return; _chinaSmlFilter = value; Update(); OnPropertyChanged("ChinaSmlFilter"); }
        }

        // MISSING CAS FILTER - This was the root cause of the CAS filter not working!
        private string _casFilter = "";
        public string CasFilter {
            get { return _casFilter; }
            set { if (_casFilter == value) return; _casFilter = value; Update(); OnPropertyChanged("CasFilter"); }
        }

        // Backward compatibility methods for the MainWindowVM calls
        public void SetColumnFilter(string columnName, string value)
        {
            Debug.WriteLine($"SetColumnFilter called: columnName='{columnName}', value='{value}'");

            switch (columnName)
            {
                case "ID":
                case "Id": // Handle both cases for ID property
                    IdFilter = value ?? "";
                    break;
                case "Name":
                    MetaboliteNameFilter = value ?? "";
                    break;
                case "InChIKey":
                    InChIKeyFilter = value ?? "";
                    break;
                case "RTs":
                case "RetentionTimes": // Add mapping for RetentionTimes property
                    RetentionTimeFilter = value ?? "";
                    break;
                case "MW":
                case "MolecularWeight": // Add mapping for MolecularWeight property
                    MzFilter = value ?? "";
                    break;
                case "CAS":
                    CasFilter = value ?? "";
                    break;
                case "CID":
                    CidFilter = value ?? "";
                    break;
                case "PrecursorType":
                    PrecursorTypeFilter = value ?? "";
                    break;
                case "CollisionEnergy":
                    CollisionEnergyFilter = value ?? "";
                    break;
                case "Cramerrules":
                    CramerRulesFilter = value ?? "";
                    break;
                case "SVHC":
                    SvhcFilter = value ?? "";
                    break;
                case "CMR":
                    CmrFilter = value ?? "";
                    break;
                case "CMRSuspect":
                    CmrSuspectFilter = value ?? "";
                    break;
                case "EDC":
                    EdcFilter = value ?? "";
                    break;
                case "IARC":
                    IarcFilter = value ?? "";
                    break;
                case "Eusml":
                    EuSmlFilter = value ?? "";
                    break;
                case "ChinaSml":
                    ChinaSmlFilter = value ?? "";
                    break;
                case "Retention time":
                    RetentionTimeFilter = value ?? "";
                    break;
                case "PrecursorMz":
                case "Precursor MZ":
                    PrecursorMzFilter = value ?? "";
                    break;
                case "IonMode":
                case "Ion mode":
                    IonModeFilter = value ?? "";
                    break;
                case "Formula":
                    FormulaFilter = value ?? "";
                    break;
                case "ExactMass":
                case "Exact mass":
                    ExactMassFilter = value ?? "";
                    break;
                case "Carcinogenicity_ISS":
                case "Carcinogenicity ISS":
                    CarcinogenicityIssFilter = value ?? "";
                    break;
                case "DNA_alerts_OASIS":
                case "DNA alerts OASIS":
                    DnaAlertsOasisFilter = value ?? "";
                    break;
                case "DNA_binding_OASIS":
                case "DNA binding OASIS":
                    DnaBindingOasisFilter = value ?? "";
                    break;
                case "DNA_binding_OECD":
                case "DNA binding OECD":
                    DnaBindingOecdFilter = value ?? "";
                    break;
                case "Protein_binding_alerts_OASIS":
                case "Protein binding alerts OASIS":
                    ProteinBindingAlertsOasisFilter = value ?? "";
                    break;
                case "Vitro_mutagenicity_alerts_ISS":
                case "Vitro mutagenicity alerts ISS":
                    VitroMutagenicityAlertsIssFilter = value ?? "";
                    break;
                case "Vivo_mutagenicity_alerts_ISS":
                case "Vivo mutagenicity alerts ISS":
                    VivoMutagenicityAlertsIssFilter = value ?? "";
                    break;
                case "Smiles":
                case "SMILES":
                    SmilesFilter = value ?? "";
                    break;
                case "InChI":
                    InchiFilter = value ?? "";
                    break;
                case "IUPACName":
                case "IUPAC name":
                    IupacNameFilter = value ?? "";
                    break;
                case "Superclass":
                    SuperclassFilter = value ?? "";
                    break;
                case "Class":
                    ClassFilter = value ?? "";
                    break;
                case "Subclass":
                    SubclassFilter = value ?? "";
                    break;
                case "Ontology":
                    OntologyFilter = value ?? "";
                    break;
                case "XLogP":
                    XlogpFilter = value ?? "";
                    break;
                case "MLogP":
                    MlogpFilter = value ?? "";
                    break;
                case "ALogP":
                    AlogpFilter = value ?? "";
                    break;
                case "TopoPSA":
                case "Topo PSA":
                    TopoPsaFilter = value ?? "";
                    break;
                case "NumBond":
                case "Num bonds":
                    NumBondFilter = value ?? "";
                    break;
                case "NumAtom":
                case "Num atoms":
                    NumAtomFilter = value ?? "";
                    break;
                case "Authors":
                    AuthorsFilter = value ?? "";
                    break;
                case "Instrument":
                    InstrumentFilter = value ?? "";
                    break;
                case "InstrumentType":
                case "Instrument type":
                    InstrumentTypeFilter = value ?? "";
                    break;
                case "Date":
                    DateFilter = value ?? "";
                    break;
                case "Num_peaks":
                case "Num peaks":
                    NumPeaksFilter = value ?? "";
                    break;
                default:
                    // Log unmapped filter attempts for debugging
                    Debug.WriteLine($"SetColumnFilter: Unmapped column name '{columnName}' with value '{value}'");
                    break;
            }

            // CRITICAL FIX: Trigger the filter refresh to apply the new filter value
            try
            {
                if (view != null)
                {
                    Debug.WriteLine($"SetColumnFilter: Refreshing view to apply filter '{columnName}' = '{value}'");
                    view.Refresh();
                }
                else
                {
                    Debug.WriteLine("SetColumnFilter: Warning - view is null, cannot refresh filters");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"SetColumnFilter: Error refreshing view: {ex.Message}");
            }
        }

        public async Task ClearAllFiltersAsync()
        {
            // Simple async wrapper for the synchronous method
            await Task.Run(() => ClearAllFilters());
        }

        #endregion

        public FilterSettingsForLibrary(ICollectionView view)
        {
            this.view = view;
        }



        /// <summary>
        /// OPTIMIZED mass spectrum filtering with performance improvements
        /// </summary>
        public bool MassSpectrumFilter(object sender)
        {
            var msp = (MassSpectrum)sender;

            // Early exit if no filters are applied - avoids unnecessary string operations
            if (string.IsNullOrEmpty(this.MetaboliteNameFilter) &&
                string.IsNullOrEmpty(this.RetentionTimeFilter) &&
                string.IsNullOrEmpty(this.MzFilter) &&
                string.IsNullOrEmpty(this.InChIKeyFilter))
            {
                return true;
            }

            // Optimized string comparisons - avoid repeated ToLower() calls
            if (!string.IsNullOrEmpty(this.MetaboliteNameFilter))
            {
                if (msp.Name == null || !msp.Name.ToLower().Contains(this.MetaboliteNameFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.RetentionTimeFilter))
            {
                if (msp.RetentionTime.ToString().IndexOf(this.RetentionTimeFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.MzFilter))
            {
                if (msp.PrecursorMz.ToString().IndexOf(this.MzFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.InChIKeyFilter))
            {
                if (msp.InChIKey == null || !msp.InChIKey.ToLower().Contains(this.InChIKeyFilter.ToLower()))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// COMPREHENSIVE compound filtering with ultra-fast performance for all filter fields
        /// </summary>
        public bool CompoundFilter(object sender)
        {
            var comp = (CompoundBean)sender;

            // CRITICAL FIX: Complete early exit check for ALL filter fields
            // This was the root cause of filters not working after ClearAllFilters!
            if (string.IsNullOrEmpty(this.IdFilter) &&
                string.IsNullOrEmpty(this.MetaboliteNameFilter) &&
                string.IsNullOrEmpty(this.RetentionTimeFilter) &&
                string.IsNullOrEmpty(this.MzFilter) &&
                string.IsNullOrEmpty(this.InChIKeyFilter) &&
                string.IsNullOrEmpty(this.CasFilter) &&
                string.IsNullOrEmpty(this.CidFilter) &&
                string.IsNullOrEmpty(this.PrecursorTypeFilter) &&
                string.IsNullOrEmpty(this.CollisionEnergyFilter) &&
                string.IsNullOrEmpty(this.CramerRulesFilter) &&
                string.IsNullOrEmpty(this.SvhcFilter) &&
                string.IsNullOrEmpty(this.CmrFilter) &&
                string.IsNullOrEmpty(this.CmrSuspectFilter) &&
                string.IsNullOrEmpty(this.EdcFilter) &&
                string.IsNullOrEmpty(this.IarcFilter) &&
                string.IsNullOrEmpty(this.EuSmlFilter) &&
                string.IsNullOrEmpty(this.ChinaSmlFilter) &&
                // MISSING FILTERS ADDED - This was causing the bug!
                string.IsNullOrEmpty(this.PrecursorMzFilter) &&
                string.IsNullOrEmpty(this.IonModeFilter) &&
                string.IsNullOrEmpty(this.FormulaFilter) &&
                string.IsNullOrEmpty(this.ExactMassFilter) &&
                string.IsNullOrEmpty(this.CarcinogenicityIssFilter) &&
                string.IsNullOrEmpty(this.DnaAlertsOasisFilter) &&
                string.IsNullOrEmpty(this.DnaBindingOasisFilter) &&
                string.IsNullOrEmpty(this.DnaBindingOecdFilter) &&
                string.IsNullOrEmpty(this.ProteinBindingAlertsOasisFilter) &&
                string.IsNullOrEmpty(this.VitroMutagenicityAlertsIssFilter) &&
                string.IsNullOrEmpty(this.VivoMutagenicityAlertsIssFilter) &&
                string.IsNullOrEmpty(this.SmilesFilter) &&
                string.IsNullOrEmpty(this.InchiFilter) &&
                string.IsNullOrEmpty(this.InchikeyFilter) &&
                string.IsNullOrEmpty(this.IupacNameFilter) &&
                string.IsNullOrEmpty(this.SuperclassFilter) &&
                string.IsNullOrEmpty(this.ClassFilter) &&
                string.IsNullOrEmpty(this.SubclassFilter) &&
                string.IsNullOrEmpty(this.OntologyFilter) &&
                string.IsNullOrEmpty(this.XlogpFilter) &&
                string.IsNullOrEmpty(this.MlogpFilter) &&
                string.IsNullOrEmpty(this.AlogpFilter) &&
                string.IsNullOrEmpty(this.TopoPsaFilter) &&
                string.IsNullOrEmpty(this.NumBondFilter) &&
                string.IsNullOrEmpty(this.NumAtomFilter) &&
                string.IsNullOrEmpty(this.AuthorsFilter) &&
                string.IsNullOrEmpty(this.InstrumentFilter) &&
                string.IsNullOrEmpty(this.InstrumentTypeFilter) &&
                string.IsNullOrEmpty(this.DateFilter) &&
                string.IsNullOrEmpty(this.NumPeaksFilter))
            {
                return true;
            }

            // ID filter - optimized string comparison
            if (!string.IsNullOrEmpty(this.IdFilter))
            {
                if (comp.Id.ToString().IndexOf(this.IdFilter, 0) < 0)
                    return false;
            }

            // Original filters - optimized string comparisons
            if (!string.IsNullOrEmpty(this.MetaboliteNameFilter))
            {
                if (comp.Name == null || !comp.Name.ToLower().Contains(this.MetaboliteNameFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.RetentionTimeFilter))
            {
                if (comp.RetentionTimes == null || !comp.RetentionTimes.Contains(this.RetentionTimeFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.MzFilter))
            {
                if (comp.MolecularWeight.ToString().IndexOf(this.MzFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.InChIKeyFilter))
            {
                if (comp.InChIKey == null || !comp.InChIKey.ToLower().Contains(this.InChIKeyFilter.ToLower()))
                    return false;
            }

            // CAS FILTER - FIXED: Now properly implemented with CAS number format support
            if (!string.IsNullOrEmpty(this.CasFilter))
            {
                if (comp.CAS == null || !comp.CAS.ToLower().Contains(this.CasFilter.ToLower()))
                    return false;
            }

            // NEW COMPREHENSIVE FILTERS - ultra-fast string matching
            if (!string.IsNullOrEmpty(this.CidFilter))
            {
                if (comp.CID == null || !comp.CID.ToLower().Contains(this.CidFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.PrecursorTypeFilter))
            {
                if (comp.PrecursorType == null || !comp.PrecursorType.ToLower().Contains(this.PrecursorTypeFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.CollisionEnergyFilter))
            {
                if (comp.CollisionEnergy.ToString().IndexOf(this.CollisionEnergyFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.CramerRulesFilter))
            {
                if (comp.Cramerrules == null || !comp.Cramerrules.ToLower().Contains(this.CramerRulesFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.SvhcFilter))
            {
                if (comp.SVHC == null || !comp.SVHC.ToLower().Contains(this.SvhcFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.CmrFilter))
            {
                if (comp.CMR == null || !comp.CMR.ToLower().Contains(this.CmrFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.CmrSuspectFilter))
            {
                if (comp.CMRSuspect == null || !comp.CMRSuspect.ToLower().Contains(this.CmrSuspectFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.EdcFilter))
            {
                if (comp.EDC == null || !comp.EDC.ToLower().Contains(this.EdcFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.IarcFilter))
            {
                if (comp.IARC == null || !comp.IARC.ToLower().Contains(this.IarcFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.EuSmlFilter))
            {
                if (comp.Eusml == null || !comp.Eusml.ToLower().Contains(this.EuSmlFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.ChinaSmlFilter))
            {
                if (comp.ChinaSml == null || !comp.ChinaSml.ToLower().Contains(this.ChinaSmlFilter.ToLower()))
                    return false;
            }

            // NEW COMPREHENSIVE FILTERS FOR ALL MISSING COLUMNS
            // Note: RetentionTimeFilter is already handled above in the existing filter logic

            if (!string.IsNullOrEmpty(this.PrecursorMzFilter))
            {
                if (comp.PrecursorMz.ToString().IndexOf(this.PrecursorMzFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.IonModeFilter))
            {
                if (comp.IonMode == null || !comp.IonMode.ToLower().Contains(this.IonModeFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.FormulaFilter))
            {
                if (comp.Formula == null || !comp.Formula.ToLower().Contains(this.FormulaFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.ExactMassFilter))
            {
                if (comp.ExactMass.ToString().IndexOf(this.ExactMassFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.CarcinogenicityIssFilter))
            {
                if (comp.Carcinogenicity_ISS == null || !comp.Carcinogenicity_ISS.ToLower().Contains(this.CarcinogenicityIssFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.DnaAlertsOasisFilter))
            {
                if (comp.DNA_alerts_OASIS == null || !comp.DNA_alerts_OASIS.ToLower().Contains(this.DnaAlertsOasisFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.DnaBindingOasisFilter))
            {
                if (comp.DNA_binding_OASIS == null || !comp.DNA_binding_OASIS.ToLower().Contains(this.DnaBindingOasisFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.DnaBindingOecdFilter))
            {
                if (comp.DNA_binding_OECD == null || !comp.DNA_binding_OECD.ToLower().Contains(this.DnaBindingOecdFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.ProteinBindingAlertsOasisFilter))
            {
                if (comp.Protein_binding_alerts_OASIS == null || !comp.Protein_binding_alerts_OASIS.ToLower().Contains(this.ProteinBindingAlertsOasisFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.VitroMutagenicityAlertsIssFilter))
            {
                if (comp.Vitro_mutagenicity_alerts_ISS == null || !comp.Vitro_mutagenicity_alerts_ISS.ToLower().Contains(this.VitroMutagenicityAlertsIssFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.VivoMutagenicityAlertsIssFilter))
            {
                if (comp.Vivo_mutagenicity_alerts_ISS == null || !comp.Vivo_mutagenicity_alerts_ISS.ToLower().Contains(this.VivoMutagenicityAlertsIssFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.SmilesFilter))
            {
                if (comp.Smiles == null || !comp.Smiles.ToLower().Contains(this.SmilesFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.InchiFilter))
            {
                if (comp.InChI == null || !comp.InChI.ToLower().Contains(this.InchiFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.InchikeyFilter))
            {
                if (comp.InChIKey == null || !comp.InChIKey.ToLower().Contains(this.InchikeyFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.IupacNameFilter))
            {
                if (comp.IUPACName == null || !comp.IUPACName.ToLower().Contains(this.IupacNameFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.SuperclassFilter))
            {
                if (comp.Superclass == null || !comp.Superclass.ToLower().Contains(this.SuperclassFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.ClassFilter))
            {
                if (comp.Class == null || !comp.Class.ToLower().Contains(this.ClassFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.SubclassFilter))
            {
                if (comp.Subclass == null || !comp.Subclass.ToLower().Contains(this.SubclassFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.OntologyFilter))
            {
                if (comp.Ontology == null || !comp.Ontology.ToLower().Contains(this.OntologyFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.XlogpFilter))
            {
                if (comp.XLogP.ToString().IndexOf(this.XlogpFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.MlogpFilter))
            {
                if (comp.MLogP.ToString().IndexOf(this.MlogpFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.AlogpFilter))
            {
                if (comp.ALogP.ToString().IndexOf(this.AlogpFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.TopoPsaFilter))
            {
                if (comp.TopoPSA.ToString().IndexOf(this.TopoPsaFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.NumBondFilter))
            {
                if (comp.NumBond.ToString().IndexOf(this.NumBondFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.NumAtomFilter))
            {
                if (comp.NumAtom.ToString().IndexOf(this.NumAtomFilter, 0) < 0)
                    return false;
            }

            if (!string.IsNullOrEmpty(this.AuthorsFilter))
            {
                if (comp.Authors == null || !comp.Authors.ToLower().Contains(this.AuthorsFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.InstrumentFilter))
            {
                if (comp.Instrument == null || !comp.Instrument.ToLower().Contains(this.InstrumentFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.InstrumentTypeFilter))
            {
                if (comp.InstrumentType == null || !comp.InstrumentType.ToLower().Contains(this.InstrumentTypeFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.DateFilter))
            {
                if (comp.Date == null || !comp.Date.ToLower().Contains(this.DateFilter.ToLower()))
                    return false;
            }

            if (!string.IsNullOrEmpty(this.NumPeaksFilter))
            {
                if (comp.Num_peaks.ToString().IndexOf(this.NumPeaksFilter, 0) < 0)
                    return false;
            }

            return true;
        }


    }
}
